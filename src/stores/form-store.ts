import { create } from 'zustand';
import { Event, EventRegistrationForm, MasterDataBillingCountry } from 'src/generated/api/dsv-public/model';
import { getBillingCountries, getEvent, getEventRegistrationForm } from 'src/generated/api/dsv-public/dsv-public';

export enum ErrorType {
  EVENT_NOT_FOUND = 'EVENT_NOT_FOUND',
  FORM_NOT_FOUND = 'FORM_NOT_FOUND',
  TECHNICAL_ERROR = 'TECHNICAL_ERROR',
  NO_EVENT_ID = 'NO_EVENT_ID',
}

type FormStoreSate = {
  event: Event | undefined;
  form: EventRegistrationForm | undefined;
  registrationCode: string | undefined;
  billingCountries: MasterDataBillingCountry[] | undefined;
  isLoading: boolean;
  error: ErrorType | null;
};

type FormStoreActions = {
  init: (eventId: number) => void;
  submitPrivateCode: (eventId: number, code: string) => void;
  initBillingCountries: () => Promise<void>;
  setError: (error: ErrorType | null) => void;
};

export const useFormStore = create<FormStoreSate & FormStoreActions>(set => ({
  event: undefined,
  form: undefined,
  registrationCode: undefined,
  billingCountries: undefined,
  isLoading: false,
  error: null,
  init: async (eventId: number) => {
    try {
      set({ isLoading: true, error: null });
      const eventData = await getEvent(eventId);
      set({ event: eventData });

      const fromData = await getEventRegistrationForm(eventId);
      set({ form: fromData });
    } catch (err: any) {
      if (err.response?.status === 404) {
        if (err.response?.data?.problemCode === 'EVENT_NOT_FOUND') {
          set({ error: ErrorType.EVENT_NOT_FOUND });
        } else if (err.response?.data?.problemCode === 'EVENT_REGISTRATION_FORM_NOT_FOUND') {
          set({ error: ErrorType.FORM_NOT_FOUND });
        } else {
          set({ error: ErrorType.EVENT_NOT_FOUND });
        }
      } else {
        set({ error: ErrorType.TECHNICAL_ERROR });
      }
    } finally {
      set({ isLoading: false });
    }
  },
  submitPrivateCode: async (eventId: number, code: string) => {
    try {
      set({ isLoading: true, registrationCode: code, error: null });
      const fromData = await getEventRegistrationForm(eventId, { registrationCode: code });
      set({ form: fromData });
    } catch (err: any) {
      if (err.response?.status === 404) {
        if (err.response?.data?.problemCode === 'EVENT_NOT_FOUND') {
          set({ error: ErrorType.EVENT_NOT_FOUND });
        } else if (err.response?.data?.problemCode === 'EVENT_REGISTRATION_FORM_NOT_FOUND') {
          set({ error: ErrorType.FORM_NOT_FOUND });
        } else {
          set({ error: ErrorType.EVENT_NOT_FOUND });
        }
      } else {
        set({ error: ErrorType.TECHNICAL_ERROR });
      }
    } finally {
      set({ isLoading: false });
    }
  },
  initBillingCountries: async () => {
    const billingCountries = await getBillingCountries();
    set({ billingCountries });
  },
  setError: (error: ErrorType | null) => set({ error }),
}));
