import { InfoIcon } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from 'src/lib/components/ui/alert';

type InfoAltertProps = {
  title: string;
  description: string;
  variant?: 'default' | 'destructive' | 'warn';
};
export function InfoMessage({ title, description, variant = 'default' }: InfoAltertProps) {
  return (
    <Alert variant={variant}>
      <InfoIcon />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription>{description}</AlertDescription>
    </Alert>
  );
}
