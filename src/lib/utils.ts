import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { Problem } from 'src/generated/api/dsv-public/model';
import dayjs from 'dayjs';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const prettyPrintDateRange = (startDate: string, endDate: string | undefined) => {
  const start = dayjs(startDate);
  const end = dayjs(endDate);

  if (endDate === undefined || start.isSame(end, 'day')) {
    return start.format('DD.MM.YYYY');
  } else if (start.isSame(end, 'year')) {
    return `${start.format('DD.MM')} - ${end.format('DD.MM.YYYY')}`;
  } else {
    return `${start.format('DD.MM.YYYY')} - ${end.format('DD.MM.YYYY')}`;
  }
};

export const prettyPrintTimeRange = (startTime: string | undefined, endTime: string | undefined) => {
  if (startTime === undefined) {
    return '-';
  }

  startTime = startTime.substring(0, 5);
  endTime = endTime?.substring(0, 5);

  if (endTime === undefined) {
    return startTime;
  }

  return `${startTime} - ${endTime}`;
};

export const prettyPrintDate = (date: string | undefined) => {
  if (date === undefined) {
    return '';
  }
  return dayjs(date).format('DD.MM.YYYY');
};

export function isProblem(obj: any): obj is Problem {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.type === 'string' &&
    typeof obj.detail === 'string' &&
    typeof obj.problemUuid === 'string' &&
    typeof obj.timestamp === 'string'
  );
}
