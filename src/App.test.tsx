import React from 'react';
import { vi, test, expect } from 'vitest';
import { render } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { RegistrationFormWrapper } from 'src/registration-form-wrapper';

test('renders app container', () => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // Deprecated
      removeListener: vi.fn(), // Deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  // Mock for window.location
  Object.defineProperty(window, 'location', {
    value: {
      href: 'http://localhost:3000/',
      origin: 'http://localhost:3000',
      hash: '',
    },
    writable: true,
  });

  const { container } = render(
    <MemoryRouter initialEntries={['/']}>
      <RegistrationFormWrapper />
    </MemoryRouter>
  );
  const appContainer = container.getElementsByClassName('App');
  expect(appContainer.length).toBe(1);
});
