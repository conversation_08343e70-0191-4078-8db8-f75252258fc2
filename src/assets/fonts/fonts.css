/* sarabun-100 - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: normal;
  font-weight: 100;
  src: url('./sarabun/sarabun-v12-latin-100.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-100.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-100.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-100.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-100.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-100.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-100italic - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: italic;
  font-weight: 100;
  src: url('./sarabun/sarabun-v12-latin-100italic.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-100italic.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-100italic.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-100italic.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-100italic.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-100italic.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-200italic - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: italic;
  font-weight: 200;
  src: url('./sarabun/sarabun-v12-latin-200italic.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-200italic.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-200italic.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-200italic.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-200italic.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-200italic.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-200 - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: normal;
  font-weight: 200;
  src: url('./sarabun/sarabun-v12-latin-200.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-200.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-200.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-200.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-200.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-200.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-300 - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: normal;
  font-weight: 300;
  src: url('./sarabun/sarabun-v12-latin-300.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-300.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-300.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-300.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-300.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-300.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-300italic - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: italic;
  font-weight: 300;
  src: url('./sarabun/sarabun-v12-latin-300italic.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-300italic.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-300italic.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-300italic.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-300italic.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-300italic.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-regular - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: normal;
  font-weight: 400;
  src: url('./sarabun/sarabun-v12-latin-regular.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-regular.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-regular.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-regular.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-regular.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-regular.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-italic - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: italic;
  font-weight: 400;
  src: url('./sarabun/sarabun-v12-latin-italic.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-italic.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-italic.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-italic.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-italic.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-italic.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-500 - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: normal;
  font-weight: 500;
  src: url('./sarabun/sarabun-v12-latin-500.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-500.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-500.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-500.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-500.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-500.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-500italic - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: italic;
  font-weight: 500;
  src: url('./sarabun/sarabun-v12-latin-500italic.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-500italic.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-500italic.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-500italic.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-500italic.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-500italic.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-600 - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: normal;
  font-weight: 600;
  src: url('./sarabun/sarabun-v12-latin-600.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-600.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-600.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-600.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-600.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-600.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-600italic - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: italic;
  font-weight: 600;
  src: url('./sarabun/sarabun-v12-latin-600italic.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-600italic.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-600italic.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-600italic.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-600italic.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-600italic.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-700 - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: normal;
  font-weight: 700;
  src: url('./sarabun/sarabun-v12-latin-700.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-700.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-700.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-700.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-700.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-700.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-700italic - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: italic;
  font-weight: 700;
  src: url('./sarabun/sarabun-v12-latin-700italic.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-700italic.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-700italic.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-700italic.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-700italic.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-700italic.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-800 - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: normal;
  font-weight: 800;
  src: url('./sarabun/sarabun-v12-latin-800.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-800.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-800.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-800.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-800.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-800.svg#Sarabun') format('svg'); /* Legacy iOS */
}
/* sarabun-800italic - latin */
@font-face {
  font-family: 'Sarabun';
  font-style: italic;
  font-weight: 800;
  src: url('./sarabun/sarabun-v12-latin-800italic.eot'); /* IE9 Compat Modes */
  src: local(''), url('./sarabun/sarabun-v12-latin-800italic.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('./sarabun/sarabun-v12-latin-800italic.woff2') format('woff2'),
    /* Super Modern Browsers */ url('./sarabun/sarabun-v12-latin-800italic.woff') format('woff'),
    /* Modern Browsers */ url('./sarabun/sarabun-v12-latin-800italic.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('./sarabun/sarabun-v12-latin-800italic.svg#Sarabun') format('svg'); /* Legacy iOS */
}
