import React from 'react';
import ReactDOM from 'react-dom/client';
import './assets/fonts/fonts.css';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { setupWorker } from 'msw/browser';
import { getDSVPublicAPIMock } from './generated/api/dsv-public/dsv-public.msw';
import { CustomAxiosConfig } from './dsv-api-axios-instance';

class RegistrationFormWebComponent extends HTMLElement {
  connectedCallback() {
    const eventIdAttr = this.getAttribute('event-id');
    const eventId = eventIdAttr ? parseInt(eventIdAttr) : undefined;
    const baseUrl = this.getAttribute('base-url');
    if (baseUrl) {
      CustomAxiosConfig.baseUrl = baseUrl;
    }
    ReactDOM.createRoot(this).render(
      <React.StrictMode>
        <App eventId={eventId} />
      </React.StrictMode>
    );
  }
}

customElements.define('registration-form', RegistrationFormWebComponent);

if (import.meta.env.VITE_API === 'mock') {
  const startWorker = async () => {
    const worker = setupWorker(...getDSVPublicAPIMock());
    await worker.start();
  };

  void startWorker();
} else if (import.meta.env.VITE_API === 'local') {
  CustomAxiosConfig.baseUrl = 'http://localhost:8100';
} else if (import.meta.env.VITE_API === 'dev') {
  CustomAxiosConfig.baseUrl = 'https://dev.termine.adventisten.cloud';
} else if (import.meta.env.VITE_API === 'prod') {
  CustomAxiosConfig.baseUrl = 'https://termine.adventisten.cloud';
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
