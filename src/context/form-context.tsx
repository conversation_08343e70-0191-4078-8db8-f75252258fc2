import React, { createContext, useCallback, useContext, useEffect, useMemo } from 'react';
import { tracker } from 'src/openreplay';
import {
  Event,
  EventRegistrationForm,
  EventRegistrationFormStatus,
  EventRegistrationRegisteredBy,
  EventRegistrationContactType,
  Problem,
} from 'src/generated/api/dsv-public/model';
import { FormStep, FormStepOrder, useFormProgressStore } from 'src/stores/form-progress-store';
import { createValidator } from 'src/validators/validator';
import { createGroupValidator } from 'src/validators/pricing-option-group-schema';
import { saveEventRegistration } from 'src/generated/api/dsv-public/dsv-public';
import { AxiosError } from 'axios';
import { phoneFieldSchema } from 'src/validators/phone-field-schema';
import { shouldDisplayField } from 'src/utils/field-display-utils';
import { isUnderage } from 'src/utils/date-utils';

type FormContextProps = {
  eventId: number;
  event: Event;
  form: EventRegistrationForm;
  availableSteps: FormStep[];
  hasUnderageParticipant: boolean;
  navigateNextStep: () => void;
  navigatePreviousStep: () => void;
  updateFieldValue: (participantIndex: number, key: string, value: any) => void;
  updatePricingOptions: (participantIndex: number, groupId: number, optionIds: number[]) => void;
  updateContactValue: (contactIndex: number, key: string, value: string) => void;
  validateParticipant: (participantIndex: number) => void;
  validatePricingOptions: (participantIndex: number, groupId: number) => void;
  scrollToError: () => void;
  submitForm: () => Promise<boolean>;
};

export const FormContext = createContext<FormContextProps>({} as FormContextProps);

type FormProviderProps = {
  children: React.ReactNode;
  eventId: number;
  event: Event;
  form: EventRegistrationForm;
};

export const FormProvider: React.FC<FormProviderProps> = ({ children, eventId, form, event }) => {
  const currentStep = useFormProgressStore(state => state.currentStep);
  const setCurrentStep = useFormProgressStore(state => state.setCurrentStep);
  const registrationSelection = useFormProgressStore(state => state.registrationSelection);
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const addParticipant = useFormProgressStore(state => state.addParticipant);
  const registrant = useFormProgressStore(state => state.registrant);
  const setRegistrant = useFormProgressStore(state => state.setRegistrant);
  const contacts = useFormProgressStore(state => state.contacts);
  const setContacts = useFormProgressStore(state => state.setContacts);
  const addContact = useFormProgressStore(state => state.addContact);
  const billingAddress = useFormProgressStore(state => state.billingAddress);
  const setBillingAddress = useFormProgressStore(state => state.setBillingAddress);
  const billingMethod = useFormProgressStore(state => state.billingMethod);
  const setRegistrationEntries = useFormProgressStore(state => state.setRegistrationEntries);
  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const updateFieldValidation = useFormProgressStore(state => state.updateFieldValidation);
  const registrationCode = useFormProgressStore(state => state.registrationCode);
  const setSubmissionResult = useFormProgressStore(state => state.setSubmissionResult);

  const hasUnderageParticipant = useMemo(() => {
    return registrationEntries.some(entry => {
      if (!entry.fields.dateOfBirth) return false;
      return isUnderage(String(entry.fields.dateOfBirth));
    });
  }, [registrationEntries]);

  const availableSteps: FormStep[] = useMemo(() => {
    let availableSteps = FormStepOrder;

    if (
      form.status === EventRegistrationFormStatus.WAIT ||
      form.status === EventRegistrationFormStatus.FULL ||
      form.status === EventRegistrationFormStatus.CLOSED
    ) {
      return [];
    }

    if (!(form.emergencyContactRequired || (hasUnderageParticipant && form.underageCheckEnabled))) {
      availableSteps = availableSteps.filter(step => step !== FormStep.EMERGENCY_CONTACT);
    }

    if (!form.billingAddressRequired) {
      availableSteps = availableSteps.filter(step => step !== FormStep.BILLING);
    }

    return availableSteps;
  }, [form, hasUnderageParticipant]);

  const updateFieldValueNoValidation = useCallback(
    (participantIndex: number, key: string, value: any) => {
      const updatedParticipant = {
        ...registrationEntries[participantIndex],
        fields: {
          ...registrationEntries[participantIndex].fields,
          [key]: value,
        },
      };
      setRegistrationEntries([
        ...registrationEntries.slice(0, participantIndex),
        updatedParticipant,
        ...registrationEntries.slice(participantIndex + 1),
      ]);
    },
    [setRegistrationEntries, registrationEntries]
  );

  const validateField = useCallback(
    (participantIndex: number, key: string, overrideValue: boolean = false) => {
      const field = form.fields.find(f => f.key === key);
      if (!field) return;

      if (!shouldDisplayField(field, participantIndex)) {
        updateFieldValidation(`${participantIndex}-${field.key}`, true);
        return;
      }

      const validator = createValidator(field, form, event, registrationSelection);
      if (validator) {
        const validation = validator.safeParse(registrationEntries[participantIndex].fields[key]);
        updateFieldValidation(
          `${participantIndex}-${field.key}`,
          validation.success,
          validation.error?.issues[0]?.message
        );

        if (overrideValue && typeof validation.data === 'string') {
          updateFieldValueNoValidation(participantIndex, key, validation.data);
        }
      }
    },
    [form, registrationEntries, updateFieldValidation, event, registrationSelection, updateFieldValueNoValidation]
  );

  const navigateNextStep = useCallback(() => {
    const nextStep = availableSteps[availableSteps.indexOf(currentStep) + 1];
    if (!nextStep) return;

    tracker.setMetadata('last-completed-step', currentStep);
    tracker.event('form_step_navigation', {
      from: currentStep,
      to: nextStep,
      direction: 'next',
    });

    if (nextStep === FormStep.PARTICIPANTS && registrationEntries.length === 0) {
      addParticipant(form);
    }

    if (currentStep === FormStep.PARTICIPANTS) {
      // override validated values for backend submission format e.g. DD.MM.YYYY -> YYYY-MM-DD
      registrationEntries.forEach((entry, index) => {
        Object.keys(entry.fields).forEach(key => {
          validateField(index, key, true);

          if (index === 0 && key === 'email') {
            tracker.setUserID(String(entry.fields[key]).trim());
          }
        });
      });
    }

    if (nextStep === FormStep.EMERGENCY_CONTACT && contacts.length === 0) {
      addContact();
    }

    if (currentStep === FormStep.EMERGENCY_CONTACT) {
      const phoneValidator = phoneFieldSchema({
        required: true,
        defaultCountry: 'CH',
      });
      setContacts(
        contacts.map(contact => {
          const validation = phoneValidator.safeParse(contact.mobile);
          if (validation.success && validation.data) {
            return { ...contact, mobile: validation.data };
          }
          return contact;
        })
      );
    }

    if (nextStep === FormStep.REGISTRANT && !registrant) {
      const phoneValidator = phoneFieldSchema({
        required: true,
        defaultCountry: 'CH',
      });

      if (hasUnderageParticipant && form.underageCheckEnabled && contacts.length > 0) {
        const parentContact = contacts.find(contact => contact.type === EventRegistrationContactType.PARENT);
        if (!parentContact) {
          tracker.event('invalid_form_state', 'No parent contact found for underage participant');
          console.error('No parent contact found for underage participant');
        }

        if (parentContact) {
          const phoneValidation = phoneValidator.safeParse(parentContact.mobile);
          setRegistrant({
            firstName: parentContact.firstName,
            lastName: parentContact.lastName,
            email: parentContact.email!,
            mobile: phoneValidation.data ? phoneValidation.data : '',
          });

          if (parentContact.email && parentContact.email.trim()) {
            tracker.setUserID(parentContact.email.trim());
          }

          return;
        }
      }

      const participantWithBothFields = registrationEntries.find(entry => entry.fields.email && entry.fields.mobile);
      const contactWithBothFields = contacts.find(contact => contact.email && contact.mobile);
      if (participantWithBothFields) {
        const email = String(participantWithBothFields.fields.email);
        const phoneValidation = phoneValidator.safeParse(participantWithBothFields.fields.mobile);
        setRegistrant({
          firstName: participantWithBothFields.fields.firstName
            ? String(participantWithBothFields.fields.firstName)
            : '',
          lastName: participantWithBothFields.fields.lastName ? String(participantWithBothFields.fields.lastName) : '',
          email: email,
          mobile: phoneValidation.data ? phoneValidation.data : '',
        });

        if (email && email.trim()) {
          tracker.setUserID(email.trim());
        }
      } else if (contactWithBothFields) {
        const email = String(contactWithBothFields.email);
        const phoneValidation = phoneValidator.safeParse(contactWithBothFields.mobile);
        setRegistrant({
          firstName: contactWithBothFields.firstName ? String(contactWithBothFields.firstName) : '',
          lastName: contactWithBothFields.lastName ? String(contactWithBothFields.lastName) : '',
          email: email,
          mobile: phoneValidation.data ? phoneValidation.data : '',
        });

        if (email && email.trim()) {
          tracker.setUserID(email.trim());
        }
      }
    }

    if (currentStep === FormStep.REGISTRANT) {
      const phoneValidator = phoneFieldSchema({
        required: true,
        defaultCountry: 'CH',
      });
      const validation = phoneValidator.safeParse(registrant?.mobile);
      if (validation.success && validation.data) {
        setRegistrant({
          ...registrant,
          mobile: validation.data || registrant?.mobile,
        } as EventRegistrationRegisteredBy);
      }
    }

    if (nextStep === FormStep.BILLING && !billingAddress) {
      const firstParticipant = registrationEntries[0].fields;
      setBillingAddress({
        firstName: registrant?.firstName || '',
        lastName: registrant?.lastName || '',
        addressLine: firstParticipant.address ? String(firstParticipant.address) : '',
        zipCode: firstParticipant.zipCode ? String(firstParticipant.zipCode) : '',
        city: firstParticipant.city ? String(firstParticipant.city) : '',
        country: firstParticipant.country ? String(firstParticipant.country) : 'CH',
      });
    }

    setCurrentStep(nextStep);
  }, [
    availableSteps,
    currentStep,
    setCurrentStep,
    addContact,
    addParticipant,
    billingAddress,
    contacts,
    contacts.length,
    form,
    hasUnderageParticipant,
    registrant,
    registrationEntries,
    setBillingAddress,
    setRegistrant,
    validateField,
  ]);

  const navigatePreviousStep = useCallback(() => {
    const previousStep = availableSteps[availableSteps.indexOf(currentStep) - 1];
    if (previousStep) {
      tracker.event('form_step_navigation', {
        from: currentStep,
        to: previousStep,
        direction: 'previous',
      });
      setCurrentStep(previousStep);
    }
  }, [availableSteps, currentStep, setCurrentStep]);

  const validatePricingOptions = useCallback(
    (participantIndex: number, groupId: number) => {
      const group = form.pricingOptionGroups?.find((group: any) => group.optionGroupId === groupId);
      if (!group) return;

      if (!shouldDisplayField(group, participantIndex)) {
        updateFieldValidation(`${participantIndex}-${groupId}`, true);
        return;
      }

      const selectedOptions =
        registrationEntries[participantIndex].options?.filter(optionId =>
          group.options.some(option => Number(option.optionId) === optionId)
        ) || [];

      const validator = createGroupValidator(group);
      if (validator) {
        const validation = validator.safeParse(selectedOptions);
        updateFieldValidation(
          `${participantIndex}-${groupId}`,
          validation.success,
          validation.error?.issues[0]?.message
        );
      }
    },
    [form, registrationEntries, updateFieldValidation, shouldDisplayField]
  );

  const validateParticipant = useCallback(
    (participantIndex: number) => {
      form.fields.forEach(field => {
        validateField(participantIndex, field.key);
      });

      form.pricingOptionGroups?.forEach(group => {
        validatePricingOptions(participantIndex, Number(group.optionGroupId));
      });
    },
    [form, validateField, validatePricingOptions]
  );

  const updateFieldValue = useCallback(
    (participantIndex: number, key: string, value: any) => {
      updateFieldValueNoValidation(participantIndex, key, value);
      validateField(participantIndex, key);
    },
    [validateField, updateFieldValueNoValidation]
  );

  const updateContactValue = useCallback(
    (contactIndex: number, key: string, value: string) => {
      const updatedContact = { ...contacts[contactIndex] };
      if (!(key in updatedContact)) return;
      // @ts-ignore
      updatedContact[key] = value;
      setContacts([...contacts.slice(0, contactIndex), updatedContact, ...contacts.slice(contactIndex + 1)]);
    },
    [contacts, setContacts]
  );

  const updatePricingOptions = useCallback(
    (participantIndex: number, groupId: number, optionIds: number[]) => {
      const group = form.pricingOptionGroups?.find((group: any) => group.optionGroupId === groupId);
      if (!group) return;

      const groupOptions = group?.options.map(o => Number(o.optionId));
      const currentParticipant = registrationEntries[participantIndex];
      const optionsWithoutCurrentGroup = currentParticipant.options?.filter(oId => !groupOptions.includes(oId)) || [];
      const updatedParticipant = {
        ...currentParticipant,
        options: optionsWithoutCurrentGroup.concat(optionIds),
      };
      setRegistrationEntries([
        ...registrationEntries.slice(0, participantIndex),
        updatedParticipant,
        ...registrationEntries.slice(participantIndex + 1),
      ]);

      validatePricingOptions(participantIndex, groupId);
    },
    [registrationEntries, setRegistrationEntries, validatePricingOptions, form.pricingOptionGroups]
  );

  const submitForm = useCallback(async () => {
    if (!registrant || !registrationSelection || !registrationEntries) {
      console.error('Registrant, registration selection, registration code or registration entries are not set');
      console.log(registrant, registrationSelection, registrationEntries);
      tracker.event('form_submission_error', {
        error: 'Missing required data',
        missingRegistrant: !registrant,
        missingRegistrationSelection: !registrationSelection,
        missingRegistrationEntries: !registrationEntries || registrationEntries.length === 0,
      });
      return false;
    }

    tracker.event('form_submission_attempt', {
      eventId,
      registrationSelection,
      participantsCount: registrationEntries.length,
      contactsCount: contacts.length,
      hasBillingAddress: !!billingAddress,
      billingMethod,
    });

    await saveEventRegistration(eventId, {
      registrant,
      registrationSelection,
      registrationCode,
      registrationEntries,
      contacts,
      billingAddress,
      billingMethod,
    })
      .then(data => {
        setSubmissionResult(data);
        tracker.setMetadata('last-completed-step', FormStep.CONFIRMATION);
        tracker.event('form_submission_success', { eventId });
      })
      .catch((error: AxiosError<Problem>) => {
        console.error(error);
        // @ts-ignore
        setSubmissionResult(error.response?.data);
        tracker.event('form_submission_failure', {
          eventId,
          status: error.response?.status,
          statusText: error.response?.statusText,
          errorType: error.response?.data?.type || 'unknown',
        });
      });

    return true;
  }, [
    registrant,
    registrationSelection,
    registrationCode,
    registrationEntries,
    contacts,
    billingAddress,
    billingMethod,
    setSubmissionResult,
    eventId,
  ]);

  useEffect(() => {
    // update URL when step changes
    const stepParam = `#step=${currentStep}`;
    if (window.location.hash !== stepParam) {
      window.history.pushState({ step: currentStep }, '', stepParam);
    }

    // handle browser back/forward navigation
    const handlePopState = (event: PopStateEvent) => {
      if (event.state?.step) {
        setCurrentStep(event.state.step);
      }
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, [currentStep, setCurrentStep]);

  const scrollToError = useCallback(() => {
    if (invalidFields.length === 0) return;
    const firstInvalidFieldKey = invalidFields[0].key;
    const element = document.getElementById(firstInvalidFieldKey);
    if (element) {
      setTimeout(() => {
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.scrollY - 70;
        window.scrollTo({ behavior: 'smooth', top: offsetPosition });
      }, 100);
    }
  }, [invalidFields]);

  return (
    <FormContext.Provider
      value={{
        eventId,
        event,
        form,
        hasUnderageParticipant,
        availableSteps,
        navigateNextStep,
        navigatePreviousStep,
        updateFieldValue,
        updatePricingOptions,
        updateContactValue,
        validateParticipant,
        validatePricingOptions,
        scrollToError,
        submitForm,
      }}
    >
      {children}
    </FormContext.Provider>
  );
};

export const useFormContext = (): FormContextProps => useContext(FormContext);
