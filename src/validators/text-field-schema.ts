import { z } from 'zod';
import i18n from 'src/i18n/i18n';

type TextFieldOptions = {
  required?: boolean;
  valuePattern?: string;
  valuePatternHumanReadable?: string;
  maxLength?: number;
};

export const textFieldSchema = (options?: TextFieldOptions) => {
  const { required = false, valuePattern, valuePatternHumanReadable, maxLength } = options || {};

  const baseSchema = z.string({
    message: i18n.t('validations.required')!,
  });
  const regexSchema = valuePattern
    ? baseSchema.regex(
        new RegExp(valuePattern),
        i18n.t('validations.pattern', { pattern: valuePatternHumanReadable || valuePattern })!
      )
    : baseSchema;
  const maxLenSchema = maxLength
    ? regexSchema.max(maxLength, i18n.t('validations.max-length', { maxLength })!)
    : regexSchema;
  return required ? maxLenSchema.min(1, i18n.t('validations.required')!) : maxLenSchema.optional();
};
