import {
  Event,
  EventRegistrationForm,
  EventRegistrationFormField,
  EventRegistrationFormFieldType,
  EventRegistrationType,
} from 'src/generated/api/dsv-public/model';
import { textFieldSchema } from 'src/validators/text-field-schema';
import { dateFieldSchema } from 'src/validators/date-field-schema';
import { phoneFieldSchema } from 'src/validators/phone-field-schema';
import { selectFieldSchema } from 'src/validators/select-field-schema';

export function createValidator(
  field: EventRegistrationFormField,
  form: EventRegistrationForm,
  event?: Event,
  registrationSelection?: EventRegistrationType
) {
  switch (field.type) {
    case EventRegistrationFormFieldType.SINGLE_LINE:
    case EventRegistrationFormFieldType.MULTI_LINE:
      return textFieldSchema({
        required: field.required,
        valuePattern: field.valuePattern,
        valuePatternHumanReadable: field.valuePatternHumanReadable,
        maxLength: field.maxLength,
      });

    case EventRegistrationFormFieldType.DATE:
      return dateFieldSchema({
        key: field.key,
        required: field.required,
        eventDate: event?.startDate,
        registeredBySelection: registrationSelection,
        ageRestriction: form.ageRestriction,
      });

    case EventRegistrationFormFieldType.PHONE_NUMBER:
      return phoneFieldSchema({
        required: field.required,
        defaultCountry: 'CH',
      });

    case EventRegistrationFormFieldType.RADIO:
    case EventRegistrationFormFieldType.SINGLE_SELECT:
      return selectFieldSchema({
        required: field.required,
        multiple: false,
      });

    case EventRegistrationFormFieldType.CHECKBOX:
    case EventRegistrationFormFieldType.MULTI_SELECT:
      return selectFieldSchema({
        required: field.required,
        multiple: true,
      });

    case EventRegistrationFormFieldType.TERMS_AND_CONDITIONS:
      return textFieldSchema({
        required: field.required,
      });

    default:
      return undefined;
  }
}
