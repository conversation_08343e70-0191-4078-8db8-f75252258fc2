import { z } from 'zod';
import i18n from 'src/i18n/i18n';

type SelectFieldOptions = {
  required?: boolean;
  multiple?: boolean;
};

export const selectFieldSchema = (options?: SelectFieldOptions) => {
  const { required = false, multiple = false } = options || {};

  if (multiple) {
    const baseSchema = z.array(z.string());
    return required ? baseSchema.min(1, i18n.t('validations.required-selection')!) : baseSchema.optional();
  }

  const baseSchema = z.string();
  return required ? baseSchema.min(1, i18n.t('validations.required')!) : baseSchema.optional();
};
