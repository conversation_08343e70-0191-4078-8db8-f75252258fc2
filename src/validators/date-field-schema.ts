import { z } from 'zod';
import dayjs, { Dayjs } from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import isLeapYear from 'dayjs/plugin/isLeapYear';
import i18n from 'src/i18n/i18n';
import { AgeRestriction } from 'src/generated/api/dsv-public/model/ageRestriction';
import { AgeRestrictionType } from 'src/generated/api/dsv-public/model/ageRestrictionType';
import { EventRegistrationType } from 'src/generated/api/dsv-public/model/eventRegistrationType';
import { getDecimalAge } from 'src/utils/date-utils';

dayjs.extend(customParseFormat);
dayjs.extend(isLeapYear);

const isValidDate = (value: string) => {
  const dotFormatRegex = /^(0?[1-9]|[12]\d|3[01])\.(0?[1-9]|1[0-2])\.(\d{2}|\d{4})$/;
  const isoFormatRegex = /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/;

  return dotFormatRegex.test(value) || isoFormatRegex.test(value);
};

const parseDateString = (inputValue: string): Dayjs => {
  const formats = [
    { format: 'D.M.YYYY', regex: /^\d{1,2}\.\d{1,2}\.\d{4}$/ },
    { format: 'D.M.YY', regex: /^\d{1,2}\.\d{1,2}\.\d{2}$/ },
    { format: 'DD.MM.YYYY', regex: /^\d{2}\.\d{2}\.\d{4}$/ },
    { format: 'DD.MM.YY', regex: /^\d{2}\.\d{2}\.\d{2}$/ },
    { format: 'YYYY-MM-DD', regex: /^\d{4}-\d{2}-\d{2}$/ },
  ];

  // First check if the date string is in a valid format
  if (!isValidDate(inputValue)) {
    return dayjs(null); // Return invalid date
  }

  for (const format of formats) {
    if (format.regex.test(inputValue)) {
      const parsed = dayjs(inputValue, format.format);
      if (parsed.isValid()) {
        return parsed;
      }
    }
  }

  return dayjs(inputValue);
};

const finalDateTransform = <T extends z.ZodType<string | undefined>>(
  schema: T,
  outputFormat: string = 'YYYY-MM-DD'
): z.ZodEffects<T, string | undefined, string | undefined> => {
  return schema.transform(value => {
    if (!value) return undefined;

    const parsed = parseDateString(value);
    if (!parsed.isValid()) return undefined;

    return parsed.format(outputFormat);
  });
};

type DateFieldOptions = {
  required?: boolean;
  outputFormat?: string;
  key?: string;
  registeredBySelection?: EventRegistrationType;
  eventDate?: Date | string | null;
  ageRestriction?: AgeRestriction;
};

export const dateFieldSchema = (options?: DateFieldOptions) => {
  const {
    required = false,
    outputFormat = 'YYYY-MM-DD',
    key,
    registeredBySelection,
    eventDate = null,
    ageRestriction,
  } = options || {};

  const baseSchema = z.string({
    message: i18n.t('validations.required')!,
  });

  const validDateSchema = baseSchema.refine(value => {
    if (!value) return !required;

    const parsed = parseDateString(value);
    return parsed.isValid();
  }, i18n.t('validations.date')!);

  const dateOfBirthSchema =
    key === 'dateOfBirth'
      ? validDateSchema.refine(value => {
          if (!value) return !required;

          const parsed = parseDateString(value);
          if (!parsed.isValid()) return false;

          // Check if date is not in the future
          return parsed.isBefore(dayjs()) || parsed.isSame(dayjs(), 'day');
        }, i18n.t('validations.date-of-birth-future')!)
      : validDateSchema;

  const shouldApplyAgeRestriction =
    key === 'dateOfBirth' &&
    registeredBySelection === EventRegistrationType.PARTICIPANT &&
    ageRestriction &&
    ageRestriction.restrictionType !== AgeRestrictionType.NO_RESTRICTION &&
    !!eventDate;

  if (!shouldApplyAgeRestriction) {
    const reqSchema = required ? dateOfBirthSchema : dateOfBirthSchema.optional();
    return finalDateTransform(reqSchema, outputFormat);
  }

  const eventDateObj = dayjs(eventDate);
  if (ageRestriction.restrictionType === AgeRestrictionType.AGE) {
    const minInclSchema = ageRestriction.minInclusive
      ? dateOfBirthSchema.refine(value => {
          const birthDate = parseDateString(value);
          if (!birthDate.isValid()) return false;

          const age = getDecimalAge(birthDate, eventDateObj);
          return age >= ageRestriction.minInclusive!;
        }, i18n.t('validations.min-age', { date: ageRestriction.minInclusive })!)
      : dateOfBirthSchema;

    const maxInclSchema = ageRestriction.maxInclusive
      ? minInclSchema.refine(value => {
          const birthDate = parseDateString(value);
          if (!birthDate.isValid()) return false;

          const age = getDecimalAge(birthDate, eventDateObj);
          return age < ageRestriction.maxInclusive!;
        }, i18n.t('validations.max-age', { date: ageRestriction.maxInclusive })!)
      : minInclSchema;

    const reqSchema = required ? maxInclSchema : maxInclSchema.optional();
    return finalDateTransform(reqSchema, outputFormat);
  } else if (ageRestriction.restrictionType === AgeRestrictionType.YEAR) {
    const minInclSchema = ageRestriction.minInclusive
      ? dateOfBirthSchema.refine(value => {
          const birthDate = parseDateString(value);
          if (!birthDate.isValid()) return false;

          return birthDate.year() >= ageRestriction.minInclusive!;
        }, i18n.t('validations.max-age-year', { date: ageRestriction.minInclusive })!)
      : dateOfBirthSchema;

    const maxInclSchema = ageRestriction.maxInclusive
      ? minInclSchema.refine(value => {
          const birthDate = parseDateString(value);
          if (!birthDate.isValid()) return false;

          return birthDate.year() <= ageRestriction.maxInclusive!;
        }, i18n.t('validations.min-age-year', { date: ageRestriction.maxInclusive })!)
      : minInclSchema;

    const reqSchema = required ? maxInclSchema : maxInclSchema.optional();
    return finalDateTransform(reqSchema, outputFormat);
  }

  const reqSchema = required ? dateOfBirthSchema : dateOfBirthSchema.optional();
  return finalDateTransform(reqSchema, outputFormat);
};
