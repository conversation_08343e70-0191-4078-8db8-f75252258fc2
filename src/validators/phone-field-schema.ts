import { z } from 'zod';
import { parsePhoneNumber, isValidPhoneNumber, getCountryCallingCode, CountryCode } from 'libphonenumber-js';
import i18n from 'src/i18n/i18n';

type PhoneFieldOptions = {
  required?: boolean;
  defaultCountry?: CountryCode;
  maxLength?: number;
};

export const phoneFieldSchema = (options?: PhoneFieldOptions) => {
  const { required = false, defaultCountry = 'CH' as CountryCode, maxLength } = options || {};

  // Create the base string schema
  let stringSchema = z.string({
    message: i18n.t('validations.required')!,
  });

  // Add maxLength constraint if provided
  if (maxLength) {
    stringSchema = stringSchema.max(maxLength, i18n.t('validations.max-length', { maxLength })!);
  }

  // Add required constraint if needed
  const baseSchema = required ? stringSchema.min(1, i18n.t('validations.required')!) : stringSchema.optional();

  return baseSchema
    .refine(value => {
      if (!value) return !required;

      try {
        const processedValue = preprocessPhoneNumber(value, defaultCountry);
        return isValidPhoneNumber(processedValue, defaultCountry);
      } catch {
        return false;
      }
    }, i18n.t('validations.mobile')!)
    .transform(value => {
      if (!value) return value;

      try {
        const processedValue = preprocessPhoneNumber(value, defaultCountry);
        const phoneNumber = parsePhoneNumber(processedValue, defaultCountry);
        return phoneNumber.format('E.164');
      } catch {
        return value;
      }
    });
};

function preprocessPhoneNumber(value: string, defaultCountry: CountryCode): string {
  let processedValue = value.trim();

  if (processedValue.startsWith('00')) {
    // Replace 00 with +
    processedValue = `+${processedValue.substring(2)}`;
  } else if (processedValue.startsWith('0') && processedValue.length > 1) {
    // Replace leading 0 with e.g. +41 for Swiss numbers
    const countryCode = getCountryCallingCode(defaultCountry);
    processedValue = `+${countryCode}${processedValue.substring(1)}`;
  }

  return processedValue;
}
