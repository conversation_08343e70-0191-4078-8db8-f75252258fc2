import { describe, it, expect } from 'vitest';
import { EventRegistrationPricingOptionGroup } from 'src/generated/api/dsv-public/model';
import { createGroupValidator } from 'src/validators/pricing-option-group-schema';

describe('createGroupValidator', () => {
  describe('required groups', () => {
    it('should validate when options count is between minOptions and maxOptions', () => {
      const group: EventRegistrationPricingOptionGroup = {
        optionGroupId: 1,
        name: 'Test Group',
        description: 'Test Description',
        required: true,
        minOptions: 2,
        maxOptions: 4,
        options: [],
      };

      const validator = createGroupValidator(group);

      // Valid cases
      expect(validator.safeParse([1, 2]).success).toBe(true);
      expect(validator.safeParse([1, 2, 3]).success).toBe(true);
      expect(validator.safeParse([1, 2, 3, 4]).success).toBe(true);

      // Invalid cases - too few options
      expect(validator.safeParse([]).success).toBe(false);
      expect(validator.safeParse([1]).success).toBe(false);

      // Invalid cases - too many options
      expect(validator.safeParse([1, 2, 3, 4, 5]).success).toBe(false);
    });
  });

  describe('optional groups', () => {
    it('should validate when options count is less than or equal to maxOptions', () => {
      const group: EventRegistrationPricingOptionGroup = {
        optionGroupId: 1,
        name: 'Test Group',
        description: 'Test Description',
        required: false,
        minOptions: 1,
        maxOptions: 3,
        options: [],
      };

      const validator = createGroupValidator(group);

      // Valid cases
      expect(validator.safeParse([]).success).toBe(true);
      expect(validator.safeParse([1]).success).toBe(true);
      expect(validator.safeParse([1, 2]).success).toBe(true);
      expect(validator.safeParse([1, 2, 3]).success).toBe(true);

      // Invalid cases - too many options
      expect(validator.safeParse([1, 2, 3, 4]).success).toBe(false);
    });
  });

  describe('edge cases', () => {
    it('should handle single selection groups (minOptions=1, maxOptions=1)', () => {
      const group: EventRegistrationPricingOptionGroup = {
        optionGroupId: 1,
        name: 'Test Group',
        description: 'Test Description',
        required: true,
        minOptions: 1,
        maxOptions: 1,
        options: [],
      };

      const validator = createGroupValidator(group);

      // Valid case
      expect(validator.safeParse([1]).success).toBe(true);

      // Invalid cases
      expect(validator.safeParse([]).success).toBe(false);
      expect(validator.safeParse([1, 2]).success).toBe(false);
    });

    it('should handle groups with same min and max options', () => {
      const group: EventRegistrationPricingOptionGroup = {
        optionGroupId: 1,
        name: 'Test Group',
        description: 'Test Description',
        required: true,
        minOptions: 3,
        maxOptions: 3,
        options: [],
      };

      const validator = createGroupValidator(group);

      // Valid case
      expect(validator.safeParse([1, 2, 3]).success).toBe(true);

      // Invalid cases
      expect(validator.safeParse([1, 2]).success).toBe(false);
      expect(validator.safeParse([1, 2, 3, 4]).success).toBe(false);
    });
  });
});
