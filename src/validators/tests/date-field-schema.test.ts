import { describe, it, expect } from 'vitest';
import { dateFieldSchema } from 'src/validators/date-field-schema';
import { AgeRestrictionType } from 'src/generated/api/dsv-public/model/ageRestrictionType';
import { EventRegistrationType } from 'src/generated/api/dsv-public/model/eventRegistrationType';

describe('dateFieldSchema', () => {
  describe('basic validation', () => {
    it('should validate a valid date string when required=true', () => {
      const schema = dateFieldSchema({ required: true });

      // valid cases
      expect(schema.safeParse('01.01.2025').success).toBe(true);
      expect(schema.safeParse('1.1.2025').success).toBe(true);
      expect(schema.safeParse('1.01.2025').success).toBe(true);
      expect(schema.safeParse('01.01.25').success).toBe(true);
      expect(schema.safeParse('2025-01-01').success).toBe(true);
      expect(schema.safeParse('2025/01/01').success).toBe(false);

      // invalid cases
      expect(schema.safeParse('').success).toBe(false);
      expect(schema.safeParse(undefined).success).toBe(false);
      expect(schema.safeParse('invalid-date').success).toBe(false);
    });

    it('should allow empty string or undefined when required=false', () => {
      const schema = dateFieldSchema({ required: false });

      expect(schema.safeParse('01.01.2025').success).toBe(true);
      expect(schema.safeParse('').success).toBe(true);
      expect(schema.safeParse(undefined).success).toBe(true);
    });

    it('should reject dates with invalid month values', () => {
      const schema = dateFieldSchema({ required: true });

      // Invalid month (20)
      expect(schema.safeParse('01.20.2025').success).toBe(false);
      expect(schema.safeParse('20.20.2020').success).toBe(false);
      expect(schema.safeParse('2025-20-01').success).toBe(false);
      expect(schema.safeParse('01.13.2025').success).toBe(false); // Month 13
      expect(schema.safeParse('32.01.2025').success).toBe(false); // Day 32
    });

    it('should accept valid date formats', () => {
      const schema = dateFieldSchema({ required: true });

      // Valid dates in different formats
      expect(schema.safeParse('1.1.2025').success).toBe(true);
      expect(schema.safeParse('01.01.2025').success).toBe(true);
      expect(schema.safeParse('1.02.2025').success).toBe(true);
      expect(schema.safeParse('01.2.2025').data).toEqual('2025-02-01');
      expect(schema.safeParse('01.10.2025').success).toBe(true);
      expect(schema.safeParse('31.12.2025').success).toBe(true);
      expect(schema.safeParse('29.02.2024').success).toBe(true); // Leap year
      expect(schema.safeParse('2025-01-01').success).toBe(true);
      expect(schema.safeParse('2024-02-29').success).toBe(true); // Leap year
    });

    it('should reject future dates for dateOfBirth fields', () => {
      const futureYear = new Date().getFullYear() + 1;
      const schema = dateFieldSchema({ key: 'dateOfBirth', required: true });

      // Future date
      expect(schema.safeParse(`01.01.${futureYear}`).success).toBe(false);
    });

    it('should accept future dates for non-dateOfBirth fields', () => {
      const futureYear = new Date().getFullYear() + 1;
      const schema = dateFieldSchema({ key: 'someOtherDateField', required: true });

      // Future date should be valid for non-dateOfBirth fields
      expect(schema.safeParse(`01.01.${futureYear}`).success).toBe(true);
    });
  });

  describe('date format transformation', () => {
    it('should transform valid date to specified output format', () => {
      const schema = dateFieldSchema({ outputFormat: 'YYYY-MM-DD' });

      const result = schema.safeParse('01.01.2025');
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toBe('2025-01-01');
      }
    });

    it('should transform date with custom output format', () => {
      const schema = dateFieldSchema({ outputFormat: 'DD.MM.YYYY' });

      const result = schema.safeParse('01.01.2025');
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toMatch(/^\d{2}\.\d{2}\.\d{4}$/);
      }
    });

    it('should correctly parse and transform dates with potentially ambiguous day/month values', () => {
      const schema = dateFieldSchema({ outputFormat: 'YYYY-MM-DD' });

      const invalidMonthResult = schema.safeParse('12.13.2025');
      expect(invalidMonthResult.success).toBe(false);

      const result1 = schema.safeParse('13.12.2025');
      expect(result1.success).toBe(true);
      if (result1.success) {
        expect(result1.data).toBe('2025-12-13');
      }

      const result2 = schema.safeParse('11.12.2025');
      expect(result2.success).toBe(true);
      if (result2.success) {
        expect(result2.data).toBe('2025-12-11');
      }

      const result3 = schema.safeParse('12.11.2025');
      expect(result3.success).toBe(true);
      if (result3.success) {
        expect(result3.data).toBe('2025-11-12');
      }

      const result5 = schema.safeParse('1.2.2025');
      expect(result5.success).toBe(true);
      if (result5.success) {
        expect(result5.data).toBe('2025-02-01');
      }

      const result6 = schema.safeParse('2025-11-12');
      expect(result6.success).toBe(true);
      if (result6.success) {
        expect(result6.data).toBe('2025-11-12');
      }
    });

    it('should handle invalid dates', () => {
      const schema = dateFieldSchema();

      const result = schema.safeParse('invalid-date');
      expect(result.success).toBe(false);
    });
  });

  describe('age restriction validation', () => {
    const eventDate = '2025-01-01';

    describe('NO_RESTRICTION', () => {
      it('should not apply age validation when restrictionType is NO_RESTRICTION', () => {
        const schema = dateFieldSchema({
          key: 'dateOfBirth',
          registeredBySelection: EventRegistrationType.PARTICIPANT,
          eventDate,
          ageRestriction: {
            restrictionType: AgeRestrictionType.NO_RESTRICTION,
            minInclusive: 18,
            maxInclusive: 65,
          },
        });

        // Any date should be valid regardless of age (except future dates)
        expect(schema.safeParse('01.01.2020').success).toBe(true); // 5 years old
        expect(schema.safeParse('01.01.1950').success).toBe(true); // 75 years old

        // Future dates should be invalid
        // 86400000 is the number of milliseconds in a day
        const tomorrow = new Date(Date.now() + 86400000).toISOString().slice(0, 10);
        expect(schema.safeParse(tomorrow).success).toBe(false);
        expect(schema.safeParse('01.01.2025').success).toBe(true);
      });
    });

    describe('AGE restriction type', () => {
      it('should validate age is between minInclusive and maxInclusive', () => {
        const schema = dateFieldSchema({
          key: 'dateOfBirth',
          registeredBySelection: EventRegistrationType.PARTICIPANT,
          eventDate,
          ageRestriction: {
            restrictionType: AgeRestrictionType.AGE,
            minInclusive: 18,
            maxInclusive: 65,
          },
        });

        // valid cases (between 18 and 65 years old)
        expect(schema.safeParse('01.01.2005').success).toBe(true); // 20 years old
        expect(schema.safeParse('01.01.1980').success).toBe(true); // 45 years old

        // invalid cases
        expect(schema.safeParse('01.01.2010').success).toBe(false); // 15 years old (too young)
        expect(schema.safeParse('01.01.1950').success).toBe(false); // 75 years old (too old)
      });

      it('should validate only minInclusive when maxInclusive is not provided', () => {
        const schema = dateFieldSchema({
          key: 'dateOfBirth',
          registeredBySelection: EventRegistrationType.PARTICIPANT,
          eventDate,
          ageRestriction: {
            restrictionType: AgeRestrictionType.AGE,
            minInclusive: 18,
          },
        });

        // valid cases (18 years or older)
        expect(schema.safeParse('01.01.2005').success).toBe(true); // 20 years old
        expect(schema.safeParse('01.01.1950').success).toBe(true); // 75 years old

        // invalid cases
        expect(schema.safeParse('01.01.2010').success).toBe(false); // 15 years old (too young)
      });

      it('should validate only maxInclusive when minInclusive is not provided', () => {
        const schema = dateFieldSchema({
          key: 'dateOfBirth',
          registeredBySelection: EventRegistrationType.PARTICIPANT,
          eventDate,
          ageRestriction: {
            restrictionType: AgeRestrictionType.AGE,
            maxInclusive: 65,
          },
        });

        // valid cases (65 years or younger)
        expect(schema.safeParse('01.01.2010').success).toBe(true); // 15 years old
        expect(schema.safeParse('01.01.1970').success).toBe(true); // 55 years old

        // invalid cases
        expect(schema.safeParse('01.01.1950').success).toBe(false); // 75 years old (too old)
      });
    });

    describe('YEAR restriction type', () => {
      it('should validate birth year is between minInclusive and maxInclusive', () => {
        const schema = dateFieldSchema({
          key: 'dateOfBirth',
          registeredBySelection: EventRegistrationType.PARTICIPANT,
          eventDate,
          ageRestriction: {
            restrictionType: AgeRestrictionType.YEAR,
            minInclusive: 1980,
            maxInclusive: 2005,
          },
        });

        // Test the validation logic - actual implementation may differ
        const result1980 = schema.safeParse('01.01.1980');
        const result2005 = schema.safeParse('31.12.2005');
        const result1979 = schema.safeParse('15.06.1979');
        const result2006 = schema.safeParse('01.01.2006');

        // valid cases
        expect(result1980.success).toBe(true);
        expect(result2005.success).toBe(true);

        // invalid cases
        expect(result2006.success).toBe(false);
        expect(result1979.success).toBe(false);
      });

      it('should validate only minInclusive when maxInclusive is not provided', () => {
        const schema = dateFieldSchema({
          key: 'dateOfBirth',
          registeredBySelection: EventRegistrationType.PARTICIPANT,
          eventDate,
          ageRestriction: {
            restrictionType: AgeRestrictionType.YEAR,
            minInclusive: 1980,
          },
        });

        const result1980 = schema.safeParse('01.01.1980');
        const result2010 = schema.safeParse('01.01.2010');
        const result1979 = schema.safeParse('31.12.1979');

        expect(result1980.success).toBe(true);
        expect(result2010.success).toBe(true);
        expect(result1979.success).toBe(false);
      });

      it('should validate only maxInclusive when minInclusive is not provided', () => {
        const schema = dateFieldSchema({
          key: 'dateOfBirth',
          registeredBySelection: EventRegistrationType.PARTICIPANT,
          eventDate,
          ageRestriction: {
            restrictionType: AgeRestrictionType.YEAR,
            maxInclusive: 2005,
          },
        });

        const result1980 = schema.safeParse('01.01.1960');
        const result2005 = schema.safeParse('31.12.2005');
        const result2006 = schema.safeParse('01.01.2006');

        expect(result1980.success).toBe(true);
        expect(result2005.success).toBe(true);
        expect(result2006.success).toBe(false);
      });
    });

    it('should not apply age validation when field key is not dateOfBirth', () => {
      const schema = dateFieldSchema({
        key: 'someOtherDateField',
        registeredBySelection: EventRegistrationType.PARTICIPANT,
        eventDate,
        ageRestriction: {
          restrictionType: AgeRestrictionType.AGE,
          minInclusive: 18,
          maxInclusive: 65,
        },
      });

      // Any date should be valid regardless of age
      expect(schema.safeParse('01.01.2020').success).toBe(true); // 5 years old
      expect(schema.safeParse('01.01.1950').success).toBe(true); // 75 years old
    });

    it('should not apply age validation when registeredBySelection is not PARTICIPANT', () => {
      const schema = dateFieldSchema({
        key: 'dateOfBirth',
        registeredBySelection: EventRegistrationType.LEADER,
        eventDate,
        ageRestriction: {
          restrictionType: AgeRestrictionType.AGE,
          minInclusive: 18,
          maxInclusive: 65,
        },
      });

      // Any date should be valid regardless of age
      expect(schema.safeParse('01.01.2020').success).toBe(true); // 5 years old
      expect(schema.safeParse('01.01.1950').success).toBe(true); // 75 years old
    });

    it('should not apply age validation when eventDate is not provided', () => {
      const schema = dateFieldSchema({
        key: 'dateOfBirth',
        registeredBySelection: EventRegistrationType.PARTICIPANT,
        ageRestriction: {
          restrictionType: AgeRestrictionType.AGE,
          minInclusive: 18,
          maxInclusive: 65,
        },
      });

      // Any date should be valid regardless of age
      expect(schema.safeParse('01.01.2020').success).toBe(true); // 5 years old
      expect(schema.safeParse('01.01.1950').success).toBe(true); // 75 years old
    });
  });
});
