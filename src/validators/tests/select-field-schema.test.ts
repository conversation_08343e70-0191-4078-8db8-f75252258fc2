import { describe, it, expect } from 'vitest';
import { selectFieldSchema } from 'src/validators/select-field-schema';

describe('selectFieldSchema', () => {
  describe('single select (multiple=false)', () => {
    it('should validate a non-empty string when required=true', () => {
      const schema = selectFieldSchema({ required: true, multiple: false });

      // Valid cases
      expect(schema.safeParse('option1').success).toBe(true);

      // Invalid cases
      expect(schema.safeParse('').success).toBe(false);
      expect(schema.safeParse(undefined).success).toBe(false);
    });

    it('should allow empty string or undefined when required=false', () => {
      const schema = selectFieldSchema({ required: false, multiple: false });

      // Valid cases
      expect(schema.safeParse('option1').success).toBe(true);
      expect(schema.safeParse('').success).toBe(true);
      expect(schema.safeParse(undefined).success).toBe(true);
    });
  });

  describe('multi select (multiple=true)', () => {
    it('should validate a non-empty array when required=true', () => {
      const schema = selectFieldSchema({ required: true, multiple: true });

      // Valid cases
      expect(schema.safeParse(['option1']).success).toBe(true);
      expect(schema.safeParse(['option1', 'option2']).success).toBe(true);

      // Invalid cases
      expect(schema.safeParse([]).success).toBe(false);
      expect(schema.safeParse(undefined).success).toBe(false);
    });

    it('should allow empty array or undefined when required=false', () => {
      const schema = selectFieldSchema({ required: false, multiple: true });

      // Valid cases
      expect(schema.safeParse(['option1']).success).toBe(true);
      expect(schema.safeParse(['option1', 'option2']).success).toBe(true);
      expect(schema.safeParse([]).success).toBe(true);
      expect(schema.safeParse(undefined).success).toBe(true);
    });
  });

  describe('default values', () => {
    it('should use default values when options are not provided', () => {
      const schema = selectFieldSchema();

      // By default, it should be a single select (multiple=false) and not required
      expect(schema.safeParse('option1').success).toBe(true);
      expect(schema.safeParse('').success).toBe(true);
      expect(schema.safeParse(undefined).success).toBe(true);
    });
  });
});
