import { describe, it, expect } from 'vitest';
import { textFieldSchema } from 'src/validators/text-field-schema';

describe('textFieldSchema', () => {
  describe('required validation', () => {
    it('should validate a non-empty string when required=true', () => {
      const schema = textFieldSchema({ required: true });

      // valid cases
      expect(schema.safeParse('hello').success).toBe(true);
      expect(schema.safeParse('123').success).toBe(true);
      expect(schema.safeParse('!@#').success).toBe(true);

      // invalid cases
      expect(schema.safeParse('').success).toBe(false);
      expect(schema.safeParse(undefined).success).toBe(false);
    });

    it('should allow empty string or undefined when required=false', () => {
      const schema = textFieldSchema({ required: false });

      expect(schema.safeParse('hello').success).toBe(true);
      expect(schema.safeParse('').success).toBe(true);
      expect(schema.safeParse(undefined).success).toBe(true);
    });
  });

  describe('pattern validation', () => {
    it('should validate strings matching the pattern', () => {
      const emailPattern = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$';
      const schema = textFieldSchema({ valuePattern: emailPattern });

      // valid cases
      expect(schema.safeParse('<EMAIL>').success).toBe(true);
      expect(schema.safeParse('<EMAIL>').success).toBe(true);

      // invalid cases
      expect(schema.safeParse('invalid-email').success).toBe(false);
      expect(schema.safeParse('test@').success).toBe(false);
      expect(schema.safeParse('@example.com').success).toBe(false);
    });

    it('should use custom error message when provided', () => {
      const emailPattern = '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$';
      const customMessage = 'Please enter a valid email address';
      const schema = textFieldSchema({
        valuePattern: emailPattern,
        valuePatternHumanReadable: customMessage,
      });

      const result = schema.safeParse('invalid-email');
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toContain(customMessage);
      }
    });

    it('should use default error message when custom message not provided', () => {
      const postalCodePattern = '^[0-9]{4,5}$';
      const schema = textFieldSchema({ valuePattern: postalCodePattern });

      const result = schema.safeParse('ABC');
      expect(result.success).toBe(false);
    });
  });

  describe('maxLength validation', () => {
    it('should validate strings within maxLength', () => {
      const schema = textFieldSchema({ maxLength: 5 });

      // valid cases
      expect(schema.safeParse('').success).toBe(true);
      expect(schema.safeParse('a').success).toBe(true);
      expect(schema.safeParse('abcde').success).toBe(true);

      // invalid cases
      expect(schema.safeParse('abcdef').success).toBe(false);
      expect(schema.safeParse('this is too long').success).toBe(false);
    });
  });

  describe('combined validations', () => {
    it('should validate with multiple constraints', () => {
      const schema = textFieldSchema({
        required: true,
        valuePattern: '^[0-9]{4,5}$',
        maxLength: 5,
      });

      // valid cases
      expect(schema.safeParse('1234').success).toBe(true);
      expect(schema.safeParse('12345').success).toBe(true);

      // invalid cases: empty
      expect(schema.safeParse('').success).toBe(false);
      expect(schema.safeParse(undefined).success).toBe(false);

      // invalid cases: pattern
      expect(schema.safeParse('abc').success).toBe(false);
      expect(schema.safeParse('123').success).toBe(false);
      expect(schema.safeParse('123456').success).toBe(false);

      // invalid cases: maxLength
      expect(schema.safeParse('123456').success).toBe(false);
    });
  });

  describe('default values', () => {
    it('should use default values when options are not provided', () => {
      const schema = textFieldSchema();

      // by default, it should be optional (required=false)
      expect(schema.safeParse('hello').success).toBe(true);
      expect(schema.safeParse('').success).toBe(true);
      expect(schema.safeParse(undefined).success).toBe(true);

      // by default, it should not have maxLength validation
      expect(
        schema.safeParse('a very very very loooooooooooooong string that would exceed most reasonable maxLength values')
          .success
      ).toBe(true);
    });
  });
});
