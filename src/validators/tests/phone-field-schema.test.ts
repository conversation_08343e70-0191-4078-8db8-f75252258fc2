import { describe, it, expect } from 'vitest';
import { phoneFieldSchema } from 'src/validators/phone-field-schema';

describe('phoneFieldSchema', () => {
  describe('validation', () => {
    it('should validate a valid phone number when required=true', () => {
      const schema = phoneFieldSchema({ required: true });

      // valid cases
      expect(schema.safeParse('+41791234567').success).toBe(true);
      expect(schema.safeParse('+41 79 123 45 67').success).toBe(true);
      expect(schema.safeParse('0791234567').success).toBe(true);
      expect(schema.safeParse('079 123 45 67').success).toBe(true);

      // invalid cases
      expect(schema.safeParse('').success).toBe(false);
      expect(schema.safeParse(undefined).success).toBe(false);
      expect(schema.safeParse('invalid').success).toBe(false);
      expect(schema.safeParse('123').success).toBe(false);
    });

    it('should allow empty string or undefined when required=false', () => {
      const schema = phoneFieldSchema({ required: false });

      expect(schema.safeParse('+41791234567').success).toBe(true);
      expect(schema.safeParse('').success).toBe(true);
      expect(schema.safeParse(undefined).success).toBe(true);
    });
  });

  describe('preprocessing helpers', () => {
    it('should replace 00 prefix with +', () => {
      const schema = phoneFieldSchema();

      const result = schema.safeParse('0041791234567');
      expect(result.success).toBe(true);
      expect(result.data).toBe('+41791234567');
    });

    it('should replace leading 0 with +41 for Swiss numbers', () => {
      const schema = phoneFieldSchema();

      const result = schema.safeParse('0791234567');
      expect(result.success).toBe(true);
      expect(result.data).toBe('+41791234567');
    });

    it('should handle numbers with spaces correctly', () => {
      const schema = phoneFieldSchema();

      const result = schema.safeParse('079 123 45 67');
      expect(result.success).toBe(true);
      expect(result.data).toBe('+41791234567');
    });
  });

  describe('formatting', () => {
    it('should format the phone number without spaces in E.164 format', () => {
      const schema = phoneFieldSchema();
      const inputs = ['+41 79 123 45 67', '079 123 45 67', '0041791234567', '0791234567'];

      for (const input of inputs) {
        const result = schema.safeParse(input);
        expect(result.success).toBe(true);
        expect(result.data).toBe('+41791234567');
      }
    });
  });

  describe('country code handling', () => {
    it('should use CH as default country code', () => {
      const schema = phoneFieldSchema();

      const result = schema.safeParse('0791234567');
      expect(result.success).toBe(true);
      expect(result.data).toBe('+41791234567');
    });

    it('should respect provided country code', () => {
      const schema = phoneFieldSchema({ defaultCountry: 'DE' });

      const result = schema.safeParse('01701234567');
      expect(result.success).toBe(true);
      expect(result.data).toBe('+491701234567');
    });
  });

  describe('maxLength validation', () => {
    it('should validate strings within maxLength', () => {
      const schema = phoneFieldSchema({ maxLength: 11 });

      expect(schema.safeParse('+41791234567').success).toBe(false); // 12 characters
      expect(schema.safeParse('0791234567').success).toBe(true); // 10 characters
    });
  });
});
