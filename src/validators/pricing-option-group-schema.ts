import { z } from 'zod';
import i18n from 'src/i18n/i18n';
import { EventRegistrationPricingOptionGroup } from 'src/generated/api/dsv-public/model';

export const createGroupValidator = (group: EventRegistrationPricingOptionGroup) => {
  const baseSchema = z.array(z.number());

  if (group.required) {
    return baseSchema
      .refine(
        optionIds => optionIds.length >= group.minOptions,
        i18n.t('validations.min-options', { count: group.minOptions })!
      )
      .refine(
        optionIds => optionIds.length <= group.maxOptions,
        i18n.t('validations.max-options', { count: group.maxOptions })!
      );
  }

  return baseSchema.refine(
    optionIds => optionIds.length <= group.maxOptions,
    i18n.t('validations.max-options', { count: group.maxOptions })!
  );
};
