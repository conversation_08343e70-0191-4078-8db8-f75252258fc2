import { useFormContext } from 'src/context/form-context';
import { ParticipantFieldWrapper } from 'src/fields/participant-field-wrapper';
import { OptionGroupWrapper } from 'src/options/option-group-wrapper';

type ParticipantFormProps = {
  participantIndex: number;
};

export function ParticipantForm({ participantIndex }: ParticipantFormProps) {
  const { form } = useFormContext();

  return (
    <div className="space-y-6">
      {form.fields
        .filter(field => !field.afterOptionsGroup)
        .map((field, index) => (
          <ParticipantFieldWrapper key={index} field={field} participantIndex={participantIndex} />
        ))}
      {form.pricingOptionGroups?.map((group, index) => (
        <OptionGroupWrapper key={index} group={group} participantIndex={participantIndex} />
      ))}
      {form.fields
        .filter(field => field.afterOptionsGroup)
        .map((field, index) => (
          <ParticipantFieldWrapper key={index} field={field} participantIndex={participantIndex} />
        ))}
    </div>
  );
}
