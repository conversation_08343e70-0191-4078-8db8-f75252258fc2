import { prettyPrintDate, prettyPrintDateRange, prettyPrintTimeRange } from 'src/lib/utils';
import { useFormContext } from 'src/context/form-context';
import { AgeRestrictionType, EventRegistrationFormStatus } from 'src/generated/api/dsv-public/model';
import { useTranslation } from 'react-i18next';

export function EventInfoSection() {
  const { t } = useTranslation();
  const { event, form } = useFormContext();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {event.startDate && (
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{t('event-info.date')}</h3>
          <p>{prettyPrintDateRange(event.startDate, event.endDate)}</p>
        </div>
      )}

      {event.startTime !== '00:00' && (
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{t('event-info.time')}</h3>
          <p>{prettyPrintTimeRange(event.startTime, event.endTime)}</p>
        </div>
      )}

      {event.organiser && (
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{t('event-info.organizer')}</h3>
          <p>{event.organiser}</p>
        </div>
      )}

      {form.registrationEnd && (
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{t('event-info.registration-deadline')}</h3>
          <p>{prettyPrintDate(form.registrationEnd)}</p>
        </div>
      )}

      {form.status === EventRegistrationFormStatus.WAITING_LIST && form.remainingWaitingListSeats && (
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{t('event-info.available-seats')}</h3>
          <p>{t('event-info.waiting-list-spots', { count: Number(form.remainingWaitingListSeats) })}</p>
        </div>
      )}

      {form.status != EventRegistrationFormStatus.WAITING_LIST && form.remainingSeats && (
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{t('event-info.available-seats')}</h3>
          <p>{t('event-info.available-spots', { available: form.remainingSeats, total: form.maxParticipants })}</p>
        </div>
      )}

      {form.ageRestriction && form.ageRestriction.restrictionType === AgeRestrictionType.AGE && (
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{t('event-info.age-restriction')}</h3>
          <p>
            {form.ageRestriction.minInclusive && form.ageRestriction.maxInclusive
              ? t('event-info.age-range', {
                  min: form.ageRestriction.minInclusive,
                  max: form.ageRestriction.maxInclusive,
                })
              : form.ageRestriction.minInclusive
              ? t('event-info.min-age', { age: form.ageRestriction.minInclusive })
              : form.ageRestriction.maxInclusive
              ? t('event-info.max-age', { age: form.ageRestriction.maxInclusive })
              : t('event-info.age-restricted')}
          </p>
        </div>
      )}

      {form.ageRestriction && form.ageRestriction.restrictionType === AgeRestrictionType.YEAR && (
        <div>
          <h3 className="text-sm font-medium text-muted-foreground">{t('event-info.age-restriction')}</h3>
          <p>
            {form.ageRestriction.minInclusive && form.ageRestriction.maxInclusive
              ? t('event-info.birth-year-range', {
                  min: form.ageRestriction.minInclusive,
                  max: form.ageRestriction.maxInclusive,
                })
              : form.ageRestriction.minInclusive
              ? t('event-info.min-birth-year', { year: form.ageRestriction.minInclusive })
              : form.ageRestriction.maxInclusive
              ? t('event-info.max-birth-year', { year: form.ageRestriction.maxInclusive })
              : t('event-info.age-restricted')}
          </p>
        </div>
      )}
    </div>
  );
}
