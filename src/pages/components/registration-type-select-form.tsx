import { RadioGroup, RadioGroupItem } from 'src/lib/components/ui/radio-group';
import { Label } from 'src/lib/components/ui/label';
import { FormNavigation } from 'src/shared/form-navigation';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { EventRegistrationType } from 'src/generated/api/dsv-public/model';
import { useFormContext } from 'src/context/form-context';
import { useTranslation } from 'react-i18next';

export function RegistrationTypeSelectForm() {
  const { t } = useTranslation();
  const { form } = useFormContext();
  const registrationSelection = useFormProgressStore(state => state.registrationSelection);
  const setRegistrationSelection = useFormProgressStore(state => state.setRegistrationSelection);
  const handleSelect = (value: string) => {
    setRegistrationSelection(value as EventRegistrationType);
  };

  return (
    <div>
      <div className="space-y-4">
        <h3 className="text-msd font-medium">{t('registration-type.title')}</h3>
        <RadioGroup value={registrationSelection} onValueChange={handleSelect}>
          {form.registeredBySelections?.includes(EventRegistrationType.PARTICIPANT) && (
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="participant" value={EventRegistrationType.PARTICIPANT} />
              <Label htmlFor="participant" className="cursor-pointer">
                {t('registration-type.participant')}
              </Label>
            </div>
          )}
          {form.registeredBySelections?.includes(EventRegistrationType.LEADER) && (
            <div className="flex items-center space-x-2">
              <RadioGroupItem id="leader" value={EventRegistrationType.LEADER} />
              <Label htmlFor="leader" className="cursor-pointer">
                {t('registration-type.leader')}
              </Label>
            </div>
          )}
        </RadioGroup>
      </div>
      <FormNavigation nextDisabled={!registrationSelection} />
    </div>
  );
}
