import { useFormProgressStore } from 'src/stores/form-progress-store';
import { isProblem } from 'src/lib/utils';
import { useTranslation } from 'react-i18next';
import parse from 'html-react-parser';
import React from 'react';
import { Alert, AlertDescription, AlertTitle } from 'src/lib/components/ui/alert';
import { Check } from 'lucide-react';
import { SubmissionError } from 'src/pages/components/submission-error';

export function FormSubmissionStatus() {
  const { t } = useTranslation();
  const submissionResult = useFormProgressStore(state => state.submissionResult);

  if (!submissionResult) {
    return <></>;
  }

  if (isProblem(submissionResult)) {
    return <SubmissionError problem={submissionResult} />;
  }

  return (
    <Alert className="bg-primary/10 border-primary">
      <Check className="h-4 w-4 text-primary" />
      <AlertTitle>{t('forms.successful-submission.title')}</AlertTitle>
      <AlertDescription>{parse(submissionResult.message)}</AlertDescription>
    </Alert>
  );
}
