import React from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from 'src/lib/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from 'src/lib/components/ui/alert';
import { AlertTriangle, OctagonX } from 'lucide-react';
import { ErrorType } from 'src/stores/form-store';

type ErrorPageProps = {
  errorType: ErrorType;
};

export function ErrorPage({ errorType }: ErrorPageProps) {
  const { t } = useTranslation();

  const getErrorDetails = () => {
    switch (errorType) {
      case ErrorType.EVENT_NOT_FOUND:
        return {
          title: t('error-page.event-not-found.title'),
          description: t('error-page.event-not-found.description'),
          message: t('forms.event-not-found.message'),
          variant: 'warn' as const,
          icon: AlertTriangle,
        };
      case ErrorType.FORM_NOT_FOUND:
        return {
          title: t('error-page.form-not-found.title'),
          description: t('error-page.form-not-found.description'),
          message: t('forms.form-not-found.message'),
          variant: 'warn' as const,
          icon: AlertTriangle,
        };
      case ErrorType.NO_EVENT_ID:
        return {
          title: t('error-page.no-event-id.title'),
          description: t('error-page.no-event-id.description'),
          message: t('forms.no-event-id.message'),
          variant: 'warn' as const,
          icon: AlertTriangle,
        };
      case ErrorType.TECHNICAL_ERROR:
        return {
          title: t('error-page.technical-error.title'),
          description: t('error-page.technical-error.description'),
          message: t('forms.error-form.title'),
          variant: 'warn' as const,
          icon: OctagonX,
        };
      default:
        return {
          title: t('error-page.default.title'),
          description: t('error-page.default.description'),
          message: t('forms.error-form.title'),
          variant: 'warn' as const,
          icon: OctagonX,
        };
    }
  };

  const errorDetails = getErrorDetails();
  const Icon = errorDetails.icon;

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <Card className="w-full">
        <CardHeader>
          <CardTitle>{errorDetails.description}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert variant={errorDetails.variant}>
            <Icon className="h-4 w-4" />
            <AlertTitle>{errorDetails.title}</AlertTitle>
            <AlertDescription>{errorDetails.message}</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}
