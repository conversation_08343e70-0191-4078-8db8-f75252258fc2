import { Problem } from 'src/generated/api/dsv-public/model';
import { AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from 'src/lib/components/ui/alert';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from 'src/lib/components/ui/accordion';
import React from 'react';
import { useTranslation } from 'react-i18next';

type SubmissionErrorProps = {
  problem: Problem;
};

export function SubmissionError({ problem }: SubmissionErrorProps) {
  const { t } = useTranslation();
  const statusCode = problem.statusCode;
  const timestamp = problem.timestamp || new Date().toISOString();

  return (
    <div className="space-y-4">
      <Alert variant="warn">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Submission Failed</AlertTitle>
        <AlertDescription>{problem.detail}</AlertDescription>
      </Alert>

      <Accordion type="single" collapsible className="w-full mt-4">
        <AccordionItem value="technical-details">
          <AccordionTrigger>{t('forms.common.technical-details')}</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 text-sm">
              {statusCode && <p className="text-start">StatusCode: {statusCode}</p>}
              {problem.problemCode && <p className="text-start">ProblemCode: {problem.problemCode}</p>}
              {problem.problemUuid && <p className="text-start">ProblemUuid: {problem.problemUuid}</p>}
              <p className="text-start">Timestamp: {timestamp}</p>
              {problem.technicalDetails && <pre>{JSON.stringify(problem.technicalDetails, null, 2)}</pre>}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
