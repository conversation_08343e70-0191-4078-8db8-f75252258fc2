import { useFormProgressStore } from 'src/stores/form-progress-store';
import { useFormContext } from 'src/context/form-context';
import { useMemo } from 'react';

export function TotalPrice() {
  const { form } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);

  const totalPriceByCurrency = useMemo(() => {
    const groupOptions = form.pricingOptionGroups?.flatMap(g => g.options) || [];
    const selectedPricingOptions = registrationEntries.flatMap(p => p.options || []);
    const currencyPrices = selectedPricingOptions.map(optionId => {
      const groupOption = groupOptions.find(go => go.optionId === optionId);
      return {
        currency: groupOption?.currency || 'CHF',
        amount: groupOption?.price || 0,
      };
    });

    const currencies = new Set(currencyPrices.map(cp => cp.currency));
    return Array.from(currencies).map(currency => ({
      currency,
      amount: currencyPrices.filter(cp => cp.currency === currency).reduce((acc, cur) => acc + cur.amount, 0),
    }));
  }, [form, registrationEntries]);

  if (totalPriceByCurrency.length === 0) {
    return <div></div>;
  }

  return (
    <div>
      <h4 className="font-semibold">Total:</h4>
      <div className="text-sm">
        {totalPriceByCurrency.map(({ currency, amount }) => `${amount} ${currency}`).join(', ')}
      </div>
    </div>
  );
}
