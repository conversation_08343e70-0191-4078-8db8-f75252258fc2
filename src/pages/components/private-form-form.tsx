import { Label } from 'src/lib/components/ui/label';
import { Input } from 'src/lib/components/ui/input';
import { Button } from 'src/lib/components/ui/button';
import { useState } from 'react';
import { useFormStore } from 'src/stores/form-store';
import { useTranslation } from 'react-i18next';
import { useFormProgressStore } from 'src/stores/form-progress-store';

export function PrivateFormForm() {
  const { t } = useTranslation();
  const event = useFormStore(state => state.event);
  const submitPrivateCode = useFormStore(state => state.submitPrivateCode);
  const setRegistrationCode = useFormProgressStore(state => state.setRegistrationCode);
  const [code, setCode] = useState<string>('');
  const [codeError, setCodeError] = useState<string | null>(null);

  const handleSubmitCode = () => {
    if (!code.trim()) {
      setCodeError(t('page.private-form.error.code-required'));
      return;
    }

    setCodeError(null);
    if (event?.eventId) {
      submitPrivateCode(event?.eventId, code);
      setRegistrationCode(code);
    } else {
      setCodeError(t('page.private-form.error.event-not-found'));
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <Label htmlFor="registrationCode">{t('page.private-form.registration-code')}</Label>
        <div className="flex space-x-2">
          <Input
            id="registrationCode"
            placeholder={t('page.private-form.placeholder')!}
            value={code}
            onChange={e => setCode(e.target.value)}
          />
          <Button onClick={handleSubmitCode}>{t('page.private-form.submit')}</Button>
        </div>
        {codeError && <p className="text-sm text-destructive">{codeError}</p>}
      </div>
    </div>
  );
}
