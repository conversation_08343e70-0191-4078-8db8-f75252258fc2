import { Card, CardContent, CardDescription, CardHeader, CardTitle } from 'src/lib/components/ui/card';
import { useFormContext } from 'src/context/form-context';
import { FormStatus } from 'src/shared/form-status';
import { EventInfoSection } from 'src/pages/components/event-info-section';
import { RegistrationTypeSelectForm } from './components/registration-type-select-form';
import { PrivateFormForm } from 'src/pages/components/private-form-form';
import { EventRegistrationFormStatus } from 'src/generated/api/dsv-public/model';
import { useTranslation } from 'react-i18next';

export function WelcomePage() {
  const { t } = useTranslation();
  const { event, form } = useFormContext();

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{t('welcome-page.title', { eventName: event.name })}</CardTitle>
        <CardDescription>{event.teaser}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <EventInfoSection />
        <FormStatus />

        {form.status === EventRegistrationFormStatus.PRIVATE && <PrivateFormForm />}
        {(form.status === EventRegistrationFormStatus.OPEN ||
          form.status === EventRegistrationFormStatus.WAITING_LIST) && <RegistrationTypeSelectForm />}
      </CardContent>
    </Card>
  );
}
