import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from 'src/lib/components/ui/card';
import { useTranslation } from 'react-i18next';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { isProblem } from 'src/lib/utils';
import { SubmissionError } from 'src/pages/components/submission-error';
import { Alert, AlertDescription, AlertTitle } from 'src/lib/components/ui/alert';
import { Check } from 'lucide-react';
import parse from 'html-react-parser';
import React from 'react';
import { Skeleton } from 'src/lib/components/ui/skeleton';
import { FormNavigation } from 'src/shared/form-navigation';

export function ConfirmationPage() {
  const { t } = useTranslation();
  const submissionResult = useFormProgressStore(state => state.submissionResult);

  if (!submissionResult) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>{t('page.confirmation.loading')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <Alert>
            <Skeleton className="h-4 w-4 rounded-full" />
            <AlertTitle>
              <Skeleton className="h-4 w-[250px] ml-5" />
            </AlertTitle>
            <AlertDescription>
              <Skeleton className="h-4 w-[200px]" />
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (isProblem(submissionResult)) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle>{t('page.confirmation.failed')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <SubmissionError problem={submissionResult} />
          <FormNavigation />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{t('page.confirmation.successful')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <Alert className="bg-primary/10 border-primary">
          <Check className="h-4 w-4 text-primary" />
          <AlertTitle>{t('forms.successful-submission.title')}</AlertTitle>
          <AlertDescription></AlertDescription>
        </Alert>
        <div className="text-md">{parse(submissionResult.message)}</div>
      </CardContent>
    </Card>
  );
}
