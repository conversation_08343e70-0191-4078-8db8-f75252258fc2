import { EventRegistrationFormField } from 'src/generated/api/dsv-public/model';
import { ParticipantField } from './participant-field';
import { useMemo } from 'react';
import { shouldDisplayField } from 'src/utils/field-display-utils';

type BehaviorAwareParticipantFieldProps = {
  field: EventRegistrationFormField;
  participantIndex: number;
};

export function ParticipantFieldWrapper({ field, participantIndex }: BehaviorAwareParticipantFieldProps) {
  const shouldDisplay = useMemo(() => {
    return shouldDisplayField(field, participantIndex);
  }, [field, participantIndex]);

  if (!shouldDisplay) {
    return null;
  }

  return <ParticipantField field={field} participantIndex={participantIndex} />;
}
