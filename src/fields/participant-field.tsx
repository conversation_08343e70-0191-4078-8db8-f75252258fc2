import { EventRegistrationFormField, EventRegistrationFormFieldType } from 'src/generated/api/dsv-public/model';
import { SingleLineField } from 'src/fields/single-line';
import { SingleSelectField } from 'src/fields/single-select';
import { MultiLineField } from 'src/fields/multi-line';
import { InfoField } from 'src/fields/info';
import { SpaceField } from 'src/fields/space';
import { CheckboxField } from 'src/fields/checkbox';
import { TermsAndConditionsField } from 'src/fields/terms-and-conditions';
import { RadioField } from 'src/fields/radio';
import { DateField } from 'src/fields/date';
import { MultiSelectField } from 'src/fields/multi-select';

type ParticipantFieldProps = {
  field: EventRegistrationFormField;
  participantIndex: number;
};

export function ParticipantField({ field, participantIndex }: ParticipantFieldProps) {
  if (field.type === EventRegistrationFormFieldType.SINGLE_LINE) {
    return <SingleLineField field={field} participantIndex={participantIndex} />;
  }

  if (field.type === EventRegistrationFormFieldType.MULTI_LINE) {
    return <MultiLineField field={field} participantIndex={participantIndex} />;
  }

  if (field.type === EventRegistrationFormFieldType.SINGLE_SELECT) {
    return <SingleSelectField field={field} participantIndex={participantIndex} />;
  }

  if (field.type === EventRegistrationFormFieldType.MULTI_SELECT) {
    return <MultiSelectField field={field} participantIndex={participantIndex} />;
  }

  if (field.type === EventRegistrationFormFieldType.INFO) {
    return <InfoField field={field} />;
  }

  if (field.type === EventRegistrationFormFieldType.SPACE) {
    return <SpaceField field={field} />;
  }

  if (field.type === EventRegistrationFormFieldType.CHECKBOX) {
    return <CheckboxField field={field} participantIndex={participantIndex} />;
  }

  if (field.type === EventRegistrationFormFieldType.RADIO) {
    return <RadioField field={field} participantIndex={participantIndex} />;
  }

  if (field.type === EventRegistrationFormFieldType.DATE) {
    return <DateField field={field} participantIndex={participantIndex} />;
  }

  if (field.type === EventRegistrationFormFieldType.TERMS_AND_CONDITIONS) {
    return <TermsAndConditionsField field={field} participantIndex={participantIndex} />;
  }

  if (field.type === EventRegistrationFormFieldType.PHONE_NUMBER) {
    return <SingleLineField field={field} participantIndex={participantIndex} />;
  }

  return <div>TODO: {field.type}</div>;
}
