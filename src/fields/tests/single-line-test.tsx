import React from 'react';
import { SingleLineField } from '../single-line';
import { EventRegistrationFormField, EventRegistrationFormFieldType } from 'src/generated/api/dsv-public/model';

export function SingleLineFieldTest() {
  // Sample field with suggestions
  const fieldWithSuggestions: EventRegistrationFormField = {
    key: 'testField',
    name: 'Test Field',
    type: EventRegistrationFormFieldType.SINGLE_LINE,
    required: true,
    placeholder: 'Enter a value or select from suggestions',
    description: 'This is a test field with suggestions',
    values: ['Apple', 'Banana', 'Cherry', 'Date', 'Elderberry'],
    maxLength: 50,
  };

  // Sample field without suggestions
  const fieldWithoutSuggestions: EventRegistrationFormField = {
    key: 'testField2',
    name: 'Test Field 2',
    type: EventRegistrationFormFieldType.SINGLE_LINE,
    required: true,
    placeholder: 'Enter a value',
    description: 'This is a test field without suggestions',
    maxLength: 50,
  };

  return (
    <div className="p-4 space-y-8">
      <h1 className="text-2xl font-bold">Single Line Field Test</h1>
      
      <div>
        <h2 className="text-xl font-semibold mb-4">With Suggestions</h2>
        <SingleLineField field={fieldWithSuggestions} participantIndex={0} />
      </div>
      
      <div>
        <h2 className="text-xl font-semibold mb-4">Without Suggestions</h2>
        <SingleLineField field={fieldWithoutSuggestions} participantIndex={0} />
      </div>
    </div>
  );
}
