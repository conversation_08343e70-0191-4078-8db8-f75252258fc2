import { EventRegistrationFormField } from 'src/generated/api/dsv-public/model';
import { Label } from 'src/lib/components/ui/label';
import { Input } from 'src/lib/components/ui/input';
import { useFormContext } from 'src/context/form-context';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { ChangeEvent, useMemo, useState } from 'react';
import dayjs from 'dayjs';

type DateFieldProps = {
  field: EventRegistrationFormField;
  participantIndex: number;
};

export function DateField({ field, participantIndex }: DateFieldProps) {
  const [touched, setTouched] = useState<boolean>(false);
  const { updateFieldValue } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const currentValue = useMemo(() => {
    if (registrationEntries && registrationEntries.length > participantIndex) {
      return registrationEntries[participantIndex].fields[field.key]
        ? String(registrationEntries[participantIndex].fields[field.key])
        : '';
    }

    return '';
  }, [field.key, registrationEntries, participantIndex]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    updateFieldValue(participantIndex, field.key, e.target.value);
  };

  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const error = invalidFields.find(invF => invF.key === `${participantIndex}-${field.key}`);
  const showError = (touched || forceErrorMessages) && error;
  const formattedValue = useMemo(() => {
    if (!currentValue || currentValue === '') return currentValue;
    const datePattern = /^\d{4}-\d{2}-\d{2}$/;
    if (datePattern.test(currentValue)) {
      const parsedDate = dayjs(currentValue, 'YYYY-MM-DD');
      if (parsedDate.isValid()) {
        return parsedDate.format('DD.MM.YYYY');
      }
    }

    return currentValue;
  }, [currentValue]);

  return (
    <div className="space-y-2" key={field.key}>
      <Label htmlFor={`${participantIndex}-${field.key}`} className="flex items-center">
        {field.name} {field.required && <span className="text-destructive ml-1">*</span>}
      </Label>
      <Input
        id={`${participantIndex}-${field.key}`}
        value={formattedValue}
        name={field.key}
        onChange={handleChange}
        onBlur={() => setTouched(true)}
        placeholder={field.placeholder || 'DD.MM.YYYY'}
        maxLength={field.maxLength}
        className={showError ? 'border-destructive' : ''}
      />
      {showError && <p className="text-sm text-destructive">{error.message}</p>}
      {field.description && <p className="text-sm text-muted-foreground">{field.description}</p>}
    </div>
  );
}
