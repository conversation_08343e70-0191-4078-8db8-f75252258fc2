import { EventRegistrationFormField } from 'src/generated/api/dsv-public/model';
import ReactMarkdown from 'react-markdown';

type InfoFieldProps = {
  field: EventRegistrationFormField;
};

export function InfoField({ field }: InfoFieldProps) {
  return (
    <div className="space-y-2" key={field.key}>
      <div className="bg-muted p-4 rounded-md">
        <h4 className="font-medium">{field.name}</h4>
        {field.description && (
          <ReactMarkdown className="text-sm text-muted-foreground mt-2">{field.description}</ReactMarkdown>
        )}
      </div>
    </div>
  );
}
