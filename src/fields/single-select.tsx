import { EventRegistrationFormField } from 'src/generated/api/dsv-public/model';
import { Label } from 'src/lib/components/ui/label';
import { useFormContext } from 'src/context/form-context';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { useEffect, useMemo, useRef, useState } from 'react';
import { cn } from 'src/lib/utils';
import { Check, ChevronsUpDown } from 'lucide-react';
import { Button } from 'src/lib/components/ui/button';
import { useTranslation } from 'react-i18next';

type SingleSelectFieldProps = {
  participantIndex: number;
  field: EventRegistrationFormField;
};

export function SingleSelectField({ participantIndex, field }: SingleSelectFieldProps) {
  const { t } = useTranslation();
  const [touched, setTouched] = useState<boolean>(false);
  const { updateFieldValue } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const currentValue = useMemo(() => {
    if (registrationEntries && registrationEntries.length > participantIndex) {
      return registrationEntries[participantIndex].fields[field.key]
        ? String(registrationEntries[participantIndex].fields[field.key])
        : undefined;
    }

    return undefined;
  }, [field.key, registrationEntries, participantIndex]);

  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [open]);

  const handleChange = (value: string) => {
    setTouched(true);
    updateFieldValue(participantIndex, field.key, value);
  };

  const filteredOptions = useMemo(() => {
    if (!field.values) return [];
    if (!searchValue) return field.values;

    return field.values.filter(option => option.toLowerCase().includes(searchValue.toLowerCase()));
  }, [field.values, searchValue]);

  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const error = invalidFields.find(invF => invF.key === `${participantIndex}-${field.key}`);
  const showError = (touched || forceErrorMessages) && error;

  return (
    <div className="space-y-2" key={field.key}>
      <Label htmlFor={`${participantIndex}-${field.key}`} className="flex items-center">
        {field.name} {field.required && <span className="text-destructive ml-1">*</span>}
      </Label>
      <div className="relative" ref={containerRef}>
        <Button
          id={`${participantIndex}-${field.key}`}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          onClick={() => setOpen(!open)}
          type="button"
          className={cn(
            'w-full justify-between',
            showError && 'border-destructive',
            !currentValue && 'text-muted-foreground'
          )}
        >
          {currentValue || field.placeholder || t('field.single-select.placeholder')}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>

        {open && (
          <div className="absolute z-50 w-full mt-1 bg-popover rounded-md border shadow-md">
            <div className="flex items-center border-b px-3">
              <input
                className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground"
                placeholder={t('field.single-select.search', { name: field.name.toLowerCase() })!}
                value={searchValue}
                onChange={e => setSearchValue(e.target.value)}
                autoFocus
              />
            </div>

            <div className="max-h-[200px] overflow-y-auto p-1">
              {filteredOptions.length === 0 ? (
                <div className="py-6 text-center text-sm">{t('field.single-select.no-options')}</div>
              ) : (
                filteredOptions.map(option => (
                  <div
                    key={option}
                    className={cn(
                      'relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground',
                      currentValue === option && 'bg-accent text-accent-foreground'
                    )}
                    onClick={() => {
                      handleChange(option);
                      setSearchValue('');
                      setOpen(false);
                    }}
                  >
                    {option}
                    {currentValue === option && <Check className="ml-auto h-4 w-4" />}
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
      {showError && <p className="text-sm text-destructive">{error.message}</p>}
      {field.description && <p className="text-sm text-muted-foreground">{field.description}</p>}
    </div>
  );
}
