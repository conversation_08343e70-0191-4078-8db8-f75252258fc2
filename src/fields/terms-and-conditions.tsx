import React, { Fragment, useMemo, useState } from 'react';
import { EventRegistrationFormField } from 'src/generated/api/dsv-public/model';
import { Checkbox } from 'src/lib/components/ui/checkbox';
import { useFormContext } from 'src/context/form-context';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { Label } from 'src/lib/components/ui/label';
import { CheckedState } from '@radix-ui/react-checkbox';
import { useTranslation } from 'react-i18next';

type TermsAndConditionsFieldProps = {
  field: EventRegistrationFormField;
  participantIndex: number;
};

export function TermsAndConditionsField({ field, participantIndex }: TermsAndConditionsFieldProps) {
  const { t } = useTranslation();
  const [touched, setTouched] = useState<boolean>(false);
  const { updateFieldValue } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const currentValue = useMemo(() => {
    if (registrationEntries && registrationEntries.length > participantIndex) {
      return registrationEntries[participantIndex].fields[field.key]
        ? String(registrationEntries[participantIndex].fields[field.key])
        : undefined;
    }

    return undefined;
  }, [field.key, registrationEntries, participantIndex]);

  const handleChange = (checked: CheckedState) => {
    setTouched(true);
    updateFieldValue(participantIndex, field.key, checked ? field.options?.url : '');
  };

  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const error = invalidFields.find(invF => invF.key === `${participantIndex}-${field.key}`);
  const showError = (touched || forceErrorMessages) && error;

  return (
    <div className="space-y-2" key={field.key}>
      <div className="flex items-center space-x-2" id={`${participantIndex}-${field.key}`}>
        <Checkbox
          id={`${participantIndex}-${field.key}-checkbox`}
          checked={currentValue === field.options?.url}
          onCheckedChange={handleChange}
        />
        <Label htmlFor={`${participantIndex}-${field.key}-checkbox`} className="text-sm gap-0">
          {field.options!['checkbox-label'] ? (
            <>
              {field.options!['checkbox-label'].split(/({{titleAndUrl}})/g).map((text, idx) => {
                if (text === '{{titleAndUrl}}') {
                  return (
                    <a key={idx} href={field.options?.url} target="_blank" rel="noreferrer" className="underline ml-1">
                      {field.name}
                    </a>
                  );
                } else {
                  return <Fragment key={idx}>{text}</Fragment>;
                }
              })}
              {field.required && <span className="text-destructive">*</span>}
            </>
          ) : (
            <>
              {t('field.terms-and-conditions.accept-the')}
              <a href={field.options?.url} className="underline ml-1" target="_blank" rel="noreferrer">
                {field.name}
              </a>
              .{field.required && <span className="text-destructive">*</span>}
            </>
          )}
        </Label>
      </div>
      {showError && <p className="text-sm text-destructive">{error.message}</p>}
    </div>
  );
}
