import { EventRegistrationFormField } from 'src/generated/api/dsv-public/model';
import { useFormContext } from 'src/context/form-context';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { useMemo, useState } from 'react';
import { Label } from 'src/lib/components/ui/label';
import { Checkbox } from 'src/lib/components/ui/checkbox';
import { CheckedState } from '@radix-ui/react-checkbox';

type CheckboxFieldProps = {
  field: EventRegistrationFormField;
  participantIndex: number;
};

export function CheckboxField({ field, participantIndex }: CheckboxFieldProps) {
  const [touched, setTouched] = useState<boolean>(false);
  const { updateFieldValue: updateParticipantField } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const currentValue = useMemo(() => {
    if (registrationEntries && registrationEntries.length > participantIndex) {
      return registrationEntries[participantIndex].fields[field.key]
        ? (registrationEntries[participantIndex].fields[field.key] as unknown as string[])
        : [];
    }

    return [];
  }, [field.key, registrationEntries, participantIndex]);

  const handleChange = (option: string) => (checked: CheckedState) => {
    setTouched(true);
    const currentValues = [...currentValue];
    if (checked) {
      if (!currentValues.includes(option)) {
        currentValues.push(option);
      }
    } else {
      const index = currentValues.indexOf(option);
      if (index !== -1) {
        currentValues.splice(index, 1);
      }
    }
    updateParticipantField(participantIndex, field.key, currentValues);
  };

  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const error = invalidFields.find(invF => invF.key === `${participantIndex}-${field.key}`);
  const showError = (touched || forceErrorMessages) && error;

  return (
    <div className="space-y-2" key={field.key}>
      <Label className="flex items-center">
        {field.name} {field.required && <span className="text-destructive ml-1">*</span>}
      </Label>
      <div className="space-y-2" id={`${participantIndex}-${field.key}`}>
        {field.values?.map((option: string) => (
          <div key={option} className="flex items-center space-x-2">
            <Checkbox
              id={`${participantIndex}-${field.key}-${option}`}
              checked={currentValue.includes(option)}
              onCheckedChange={handleChange(option)}
            />
            <Label htmlFor={`${participantIndex}-${field.key}-${option}`} className="cursor-pointer">
              {option}
            </Label>
          </div>
        ))}
      </div>
      {showError && <p className="text-sm text-destructive">{error.message}</p>}
      {field.description && <p className="text-sm text-muted-foreground">{field.description}</p>}
    </div>
  );
}
