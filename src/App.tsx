import React, { useEffect } from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { tracker } from './openreplay';
import { RegistrationFormWrapper } from 'src/registration-form-wrapper';

type AppProps = {
  eventId?: number;
};

const App: React.FC<AppProps> = ({ eventId }) => {
  useEffect(() => {
    tracker.start();
  }, []);

  const router = createBrowserRouter([
    {
      path: '*',
      element: <RegistrationFormWrapper eventId={eventId} />,
    },
  ]);

  return <RouterProvider router={router} />;
};

export default App;
