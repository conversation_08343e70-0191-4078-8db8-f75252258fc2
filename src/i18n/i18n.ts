import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

import translationsEn from './locales/en.json';
import translationsDe from './locales/de.json';
import { initReactI18next } from 'react-i18next';

export const SupportedLanguages = {
  en: 'en',
  de: 'de',
};

const resources = {
  [SupportedLanguages.en]: {
    translation: translationsEn,
  },
  [SupportedLanguages.de]: {
    translation: translationsDe,
  },
};

void i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    detection: {
      order: ['sessionStorage', 'localStorage', 'navigator'],
      caches: ['sessionStorage', 'localStorage'],
      lookupSessionStorage: 'lang',
      lookupLocalStorage: 'lang',
    },
    resources,
    fallbackLng: {
      default: [SupportedLanguages.de],
    },
    supportedLngs: Object.values(SupportedLanguages),
    interpolation: {
      escapeValue: false,
    },
    debug: true,
    returnNull: false,
  });

export default i18n;
