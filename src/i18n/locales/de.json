{"forms.billing.address-line": "Adresszeile", "forms.billing.billing-method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forms.billing.billing-method-email": "E-Mail", "forms.billing.billing-method-letter": "Post", "forms.billing.city": "Ort", "forms.billing.country": "Land", "forms.billing.email": "E-Mail", "forms.billing.first-name": "<PERSON><PERSON><PERSON>", "forms.billing.last-name": "Nachname", "forms.billing.title": "Re<PERSON>nungsadress<PERSON>", "forms.billing.intro": "Bitte überprüfe deine Rechnungsadresse oder passe sie an:", "forms.billing.zipCode": "PLZ", "forms.event-not-found.message": "Event nicht gefunden. Bitte geben Sie eine gültige Event-ID an.", "forms.form-not-found.message": "Formular nicht gefunden. Bitte geben Sie eine gültige Event-ID an.", "forms.inactive-form.message": "Inaktives Formular", "forms.inactive-form.status-closed": "Das Anmeldefenster ist geschlossen. Es werden aktuell keine weiteren Anmeldungen entgegengenommen.", "forms.inactive-form.status-full": "Die Veranstaltung ist komplett ausgebucht. Es können keine weiteren Plätze gebucht werden.", "forms.inactive-form.status-wait": "Das Anmeldefenster ist noch nicht offen. Es öffnet sich am", "forms.inactive-form.status-waiting-list": "Alle bereits verfügbaren Plätze sind belegt. Es ist jedoch möglich, sich in die Warteliste einzutragen.", "forms.error-form.title": "<PERSON>t uns leid, ein <PERSON> ist passiert.", "forms.common.title": "<PERSON><PERSON><PERSON><PERSON> für {{event.name}}", "forms.initial-questionnaire.title": "<PERSON><PERSON><PERSON><PERSON>", "forms.initial-questionnaire.instructions": "<PERSON>te wähle aus, wen du anmelden möchtest", "forms.initial-questionnaire.selection.myself": "<PERSON><PERSON> (ab 18)", "forms.initial-questionnaire.selection.myself.description": "Hier meldest du dich als <1>Einzelperson</1> an, wenn du volljährig bist.", "forms.initial-questionnaire.selection.my-family": "Paare / Meine Familie", "forms.initial-questionnaire.selection.my-family.description": "Hier meldest du dich und deine Familie oder deinen Ehepartner (<strong>ab zwei Personen</strong>) an. Die Personen müssen alle im gleichen Haushalt leben.", "forms.initial-questionnaire.selection.family-members": "Kinder / Familienmitglieder", "forms.initial-questionnaire.selection.family-members.description": "Hier meldest du deine <strong><PERSON><PERSON></strong>, aber nicht dich selbst an.", "forms.initial-questionnaire.selection.myself-and-others": "<PERSON><PERSON> sel<PERSON>t und andere", "forms.initial-questionnaire.selection.myself-and-others.description": "Hier meldest du dich selbst und andere Personen an, die nicht im selben Ha<PERSON>t leben wie du.", "forms.initial-questionnaire.selection.leader": "Leitungsteam", "forms.initial-questionnaire.selection.leader.description": "Hier meldest du dich an, falls du Teil des Leitungsteams bist.", "forms.event-info.title": "Informationen zum Anlass", "forms.event-info.event": "<PERSON><PERSON>", "forms.event-info.date": "Datum", "forms.event-info.time": "Zeit", "forms.event-info.organizer": "Verantwortlicher", "forms.event-info.registration-deadline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forms.event-info.available-seats": "Verfügbare Plätze", "forms.event-info.waiting-list": "Wartelist<PERSON>", "forms.common.loading": "Laden...", "forms.common.back": "Zurück", "forms.common.submit": "Anmelden", "forms.common.incorrect-field-pattern": "Die Eingabe sollte die folgende Form haben: {{pattern}}", "forms.common.incorrect-field-pattern.country": "Bitte überprüfe dein Land.", "forms.common.incorrect-field-pattern.generic": "Bitte überprüfe deine Eingabe.", "forms.common.incorrect-field-pattern.required": "<PERSON><PERSON> ist erforderlich.", "forms.common.incorrect-field-pattern.email": "Bitte überprüfe deine E-Mail-Adresse.", "forms.common.incorrect-field-pattern.mobile": "Bitte überprüfe deine Mobilnummer.", "forms.common.incorrect-field-pattern.max-length": "Die Eingabe darf maximal {{maxLength}} <PERSON><PERSON><PERSON> lang sein.", "forms.common.incorrect-field-pattern.zip-code": "Bitte überprüfe deine Postleitzahl.", "forms.common.technical-details": "Technische Details", "forms.multi-form.title": "<PERSON><PERSON><PERSON>", "forms.multi-form.additional-participant": "<PERSON><PERSON><PERSON>", "forms.multi-form.submit": "Anmelden", "forms.multi-form.delete-dialog.title": "Willst du diesen Teilnehmer wirklich löschen?", "forms.multi-form.delete-dialog.description": "Das löschen dieses Teilnehmers kann nicht rückgängig gemacht werden.", "forms.multi-form.delete-dialog.abort": "Abbrechen", "forms.multi-form.delete-dialog.submit": "Löschen", "forms.registrant.title": "Informationen über Dich", "forms.registrant.title.family-members": "Informationen der Erziehungsberechtigten Person", "forms.registrant.description.family-members": "Hier gibt die erziehungsberechtigte Person ihre Daten ein, damit wir sie informieren und für Rückfragen kontaktieren können. Sie selbst ist nicht angemeldet.", "forms.common.next": "<PERSON><PERSON>", "forms.registrant.first-name": "<PERSON><PERSON><PERSON>", "forms.registrant.last-name": "Nachname", "forms.registrant.email": "E-Mail", "forms.registrant.mobile": "Mobilnummer", "forms.single-form.title": "Einzelformular", "forms.single-form.submit": "Anmelden", "forms.stepper.billing-info": "Re<PERSON>nungsadress<PERSON>", "forms.stepper.info": "Info zum Event", "forms.stepper.identification": "Identifikation", "forms.stepper.registration-form": "Anmeldeformular", "forms.stepper.registration-confirmation": "Bestätigung", "forms.submission-failed.message": "Leider ist bei der Übermittlung ein Fehler aufgetreten. Bitte versuchen Sie es später noch einmal.", "forms.successful-submission.title": "Anmeldeprozess fast abgeschlossen", "form.toggleable-registration.new-participant": "<PERSON><PERSON><PERSON>", "field.terms-and-conditions.accept-the": "Ich akzeptiere die ", "error-page.event-not-found.title": "Veranstaltung nicht gefunden", "error-page.event-not-found.description": "Wir konnten die angeforderte Veranstaltung nicht finden.", "error-page.form-not-found.title": "Formular nicht gefunden", "error-page.form-not-found.description": "Das Anmeldeformular für diese Veranstaltung ist nicht verfügbar.", "error-page.technical-error.title": "Technischer Fehler", "error-page.technical-error.description": "Ein unerwarteter technischer Fehler ist aufgetreten.", "error-page.default.title": "<PERSON><PERSON>", "error-page.default.description": "Ein unerwarteter Fehler ist aufgetreten.", "error-page.no-event-id.title": "Keine Event-ID", "error-page.no-event-id.description": "<PERSON>s wurde keine Event-ID angegeben", "forms.no-event-id.message": "Bitte geben Sie eine Event-ID an, um das Anmeldeformular anzuzeigen.", "event-info.date": "Datum", "event-info.time": "Zeit", "event-info.organizer": "Veranstalter", "event-info.registration-deadline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event-info.available-seats": "Verfügbare Plätze", "event-info.waiting-list-spots": "Warteliste: {{count}} Plätze verfügbar", "event-info.available-spots": "{{available}} von {{total}} Plätzen verfügbar", "event-info.age-restriction": "Altersbeschränkung", "event-info.age-range": "{{min}} bis {{max}} Jahre alt", "event-info.min-age": "Mindestalter: {{age}} Jahre", "event-info.max-age": "Höchstalter: {{age}} Jahre", "event-info.age-restricted": "Altersbeschränkt", "event-info.birth-year-range": "Geburtsjahr z<PERSON> {{min}} und {{max}}", "event-info.min-birth-year": "Mindestgeburtsjahr: {{year}}", "event-info.max-birth-year": "Höchstgeburtsjahr: {{year}}", "field.single-select.placeholder": "Option auswählen...", "field.single-select.search": "{{name}} suchen...", "field.single-select.no-options": "Keine Option gefunden.", "validations.required": "<PERSON><PERSON> ist erforderlich.", "validations.email": "Bitte überprüfe deine E-Mail-Adresse.", "validations.mobile": "Bitte überprüfe deine Telefonnummer.", "validations.max-length": "Die Eingabe darf maximal {{maxLength}} <PERSON><PERSON><PERSON> lang sein.", "validations.pattern": "Die Eingabe sollte die folgende Form haben: {{pattern}}", "validations.required-selection": "Mindestens eine Option muss ausgewählt sein.", "validations.date": "<PERSON>te gebe ein gültiges Datum ein.", "validations.min-age": "Du bist zu jung, um teilzunehmen. (Du musst mindestens {{date}} Jahre alt sein)", "validations.max-age": "Du bist zu alt, um teilzunehmen. (Du musst maximal {{date}} Jahre alt sein)", "validations.min-age-year": "Du bist zu jung, um teilzunehmen. (Geburtsdatum muss vor {{date}} liegen)", "validations.max-age-year": "Du bist zu alt, um teilzunehmen. (Geburtsdatum muss nach {{date}} liegen)", "validations.min-options": "<PERSON>te wähle mindestens {{count}} Optionen aus.", "validations.max-options": "Bitte wähle höchstens {{count}} Optionen aus.", "validations.date-of-birth-future": "Das Geburtsdatum kann nicht in der Zukunft liegen.", "registration-type.title": "Anmeldungsart", "registration-type.participant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registration-type.leader": "<PERSON><PERSON>", "welcome-page.title": "Anmeldung für {{eventName}}", "stepper.event-details": "Informationen", "stepper.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stepper.emergency-contact": "Notfallkontakt", "stepper.parental-responsibility": "Erziehungsberechtigte", "stepper.registrant": "Anmelder", "stepper.billing": "<PERSON><PERSON><PERSON><PERSON>", "stepper.confirmation": "Bestätigung", "stepper.step-count": "Schritt {{current}} von {{total}}", "shared.navigation.back": "Zurück", "shared.navigation.next": "<PERSON><PERSON>", "shared.navigation.cancel": "Abbrechen", "shared.navigation.delete": "Löschen", "registrant.title": "Anmelder-Informationen", "registrant.description": "Wer füllt diese Anmeldung aus?", "registrant.participants": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registrant.someone-else": "<PERSON><PERSON>", "registrant.first-name": "<PERSON><PERSON><PERSON>", "registrant.last-name": "Nachname", "contact.add-another": "Weiteren Kontakt hinzufügen", "contact.delete.title": "Kontakt löschen", "contact.delete.description": "Sind <PERSON> sicher, dass Sie diesen Kontakt löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "date-picker.placeholder": "Da<PERSON> ausw<PERSON>en", "date-picker.year": "<PERSON><PERSON><PERSON>", "shared.form-status.wait.title": "Anmeldung noch nicht ge<PERSON>ffnet", "shared.form-status.wait.description": "Das Anmeldefenster öffnet am {{date}}.", "shared.form-status.private.title": "Private Anmeldung", "shared.form-status.private.description": "Diese Anmeldung ist privat. Bitte geben Sie den Anmeldecode ein, um fortzufahren.", "shared.form-status.waiting-list.title": "Wartelist<PERSON>", "shared.form-status.waiting-list.description": "Alle verfügbaren Plätze sind belegt. Sie können sich jedoch auf die Warteliste setzen lassen.", "shared.form-status.full.title": "Veranstaltung ausgebucht", "shared.form-status.full.description": "Die Veranstaltung ist komplett ausgebucht. Es können keine weiteren Plätze gebucht werden.", "shared.form-status.closed.title": "Anmeldung geschlossen", "shared.form-status.closed.description": "Das Anmeldefenster ist geschlossen. Aktuell werden keine weiteren Anmeldungen entgegengenommen.", "page.participants.title": "Teilnehmer Details", "page.participants.description": "Bitte geben Sie Details für jeden Teilnehmer an", "page.participants.add-another": "Weiteren Teilnehmer hinzufügen", "page.participants.delete.title": "Teilnehmer löschen", "page.participants.delete.description": "Sind <PERSON> sic<PERSON>, dass Sie diesen Teilnehmer löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.", "page.participants.delete.cancel": "Abbrechen", "page.participants.delete.confirm": "Löschen", "page.billing.title": "Re<PERSON>nungsadress<PERSON>", "page.billing.description": "Bitte geben Sie Ihre Rechnungsinformationen an", "page.billing.first-name": "<PERSON><PERSON><PERSON>", "page.billing.last-name": "Nachname", "page.billing.address": "<PERSON><PERSON><PERSON>", "page.billing.zip-code": "PLZ", "page.billing.city": "Ort", "page.billing.country": "Land", "page.billing.billing-method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "page.billing.country-placeholder": "Land auswählen", "page.billing.method-email": "E-Mail", "page.billing.method-letter": "Brief", "page.billing.submit": "<PERSON><PERSON> abs<PERSON><PERSON>", "page.billing.error.first-name-required": "Vorname ist erforderlich", "page.billing.error.last-name-required": "Nachname ist erforderlich", "page.billing.error.address-required": "<PERSON>resse ist erforderlich", "page.billing.error.zip-code-required": "PLZ ist erforderlich", "page.billing.error.zip-code-format": "PLZ muss 4-5 <PERSON><PERSON><PERSON> enthal<PERSON>", "page.billing.error.city-required": "Ort ist erford<PERSON>lich", "page.billing.error.country-required": "Land ist erforderlich", "page.confirmation.loading": "Laden...", "page.confirmation.failed": "Übermittlung fehlgeschlagen", "page.confirmation.successful": "Übermittlung erfolgreich", "page.private-form.registration-code": "Registrierungscode", "page.private-form.placeholder": "Registrierungscode eingeben", "page.private-form.submit": "<PERSON><PERSON><PERSON><PERSON>", "page.private-form.error.code-required": "Bitte geben Si<PERSON> einen Registrierungscode ein", "page.private-form.error.event-not-found": "Event-ID nicht gefunden", "page.contact.validation-error": "<PERSON>te füllen Si<PERSON> alle erforderlichen Felder aus, bevor <PERSON> fort<PERSON>hren", "page.contact.error.type-required": "Kontakttyp ist erforderlich", "page.contact.error.first-name-required": "Vorname ist erforderlich", "page.contact.error.last-name-required": "Nachname ist erforderlich", "page.contact.error.mobile-required": "Telefonnummer ist erforderlich", "page.contact.error.mobile-format": "Bitte geben Si<PERSON> eine gültige Telefonnummer ein", "page.contact.error.email-format": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "page.registrants.error.first-name-required": "Vorname ist erforderlich", "page.registrants.error.last-name-required": "Nachname ist erforderlich", "page.registrants.error.email-required": "E-Mail ist erforderlich", "page.registrants.error.email-format": "Bitte geben Si<PERSON> eine gültige E-Mail-Adresse ein", "page.registrants.error.mobile-required": "Telefonnummer ist erforderlich", "page.registrants.error.mobile-format": "Bitte geben Si<PERSON> eine gültige Telefonnummer ein", "registrant.parent-only-message": "Bei der Anmeldung minderjähriger Teilnehmer können nur Eltern/Erziehungsberechtigte als Anmelder ausgewählt werden.", "page.contact.type.PARENT": "Elternteil/Erziehungsberechtigter", "page.contact.type.PARTNER": "Partner", "page.contact.type.GRAND_PARENT": "Großelternteil", "page.contact.type.RELATIVE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "page.contact.type.WELL_KNOWN": "<PERSON><PERSON><PERSON>", "page.contact.type.placeholder": "Kontakttyp auswählen", "page.contact.label.type": "<PERSON><PERSON>", "page.contact.label.first-name": "<PERSON><PERSON><PERSON>", "page.contact.label.last-name": "Nachname", "page.contact.label.phone": "Telefonnummer", "page.contact.label.email": "E-Mail", "page.contact.title.emergency": "Notfallkontakt", "page.contact.title.parental": "Erziehungsberechtigte Person", "page.contact.description.emergency": "Bitte geben Sie Notfallkontaktinformationen an", "page.contact.description.parental": "Bitte geben Sie Kontaktinformationen für den Elternteil oder Erziehungsberechtigten an, der für den/die minderjährigen Teilnehmer verantwortlich ist", "page.contact.number": "Kontakt {{count}}", "page.contact.parent-only-message": "Bei der Anmeldung minderjähriger Teilnehmer sind nur Eltern/Erziehungsberechtigte als Kontakte zulässig. Zusätzliche Kontakte können nicht hinzugefügt werden."}