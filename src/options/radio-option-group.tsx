import { RadioGroup, RadioGroupItem } from 'src/lib/components/ui/radio-group';
import { EventRegistrationPricingOptionGroup } from 'src/generated/api/dsv-public/model';
import { Label } from 'src/lib/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from 'src/lib/components/ui/tooltip';
import { Info } from 'lucide-react';
import { useFormContext } from 'src/context/form-context';
import { useMemo } from 'react';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { cn } from 'src/lib/utils';
import { shouldDisplayField } from 'src/utils/field-display-utils';

type RadioOptionProps = {
  participantIndex: number;
  group: EventRegistrationPricingOptionGroup;
};

export function RadioOptionGroup({ group, participantIndex }: RadioOptionProps) {
  const { updatePricingOptions } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const selectedOptionId = useMemo(() => {
    const selectedOption = group.options.find(option =>
      registrationEntries[participantIndex].options?.includes(Number(option.optionId))
    );
    return selectedOption ? String(selectedOption.optionId) : undefined;
  }, [registrationEntries, group, participantIndex]);

  const options = useMemo(
    () => group.options.filter(option => shouldDisplayField(option, participantIndex)),
    [group, participantIndex]
  );

  const handleChange = (value: string) => {
    updatePricingOptions(participantIndex, Number(group.optionGroupId), [Number(value)]);
  };

  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const error = invalidFields.find(invF => invF.key === `${participantIndex}-${group.optionGroupId}`);
  const showError = forceErrorMessages && error;

  return (
    <div className="space-y-2">
      <h3 className={cn('text-md font-medium flex items-center', showError && 'border-destructive')}>
        {group.name} {group.required && <span className="text-destructive ml-1">*</span>}
      </h3>
      <RadioGroup
        id={`${participantIndex}-${group.optionGroupId}`}
        value={selectedOptionId || ''}
        onValueChange={handleChange}
        className="space-y-2"
      >
        {options.map(option => (
          <Label
            key={option.optionId}
            htmlFor={`${participantIndex}-${group.optionGroupId}-${option.optionId}`}
            className="flex items-center justify-between p-3 border rounded-md cursor-pointer"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem
                value={String(option.optionId)}
                id={`${participantIndex}-${group.optionGroupId}-${option.optionId}`}
              />
              <div className="flex items-center">
                {option.name}
                {option.description && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground ml-2" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{option.description}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </div>
            {option.price && option.currency && (
              <div className="font-medium">
                {option.price.toFixed(2)} {option.currency}
              </div>
            )}
          </Label>
        ))}
      </RadioGroup>
      {showError && <p className="text-sm text-destructive">{error?.message}</p>}
      {group.description && <p className="text-sm text-muted-foreground">{group.description}</p>}
    </div>
  );
}
