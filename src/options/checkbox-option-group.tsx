import { Info } from 'lucide-react';
import { EventRegistrationPricingOptionGroup } from 'src/generated/api/dsv-public/model';
import { Checkbox } from 'src/lib/components/ui/checkbox';
import { Label } from 'src/lib/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from 'src/lib/components/ui/tooltip';
import { useCallback, useMemo } from 'react';
import { useFormContext } from 'src/context/form-context';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { cn } from 'src/lib/utils';
import { shouldDisplayField } from 'src/utils/field-display-utils';

type CheckboxOptionGroupProps = {
  participantIndex: number;
  group: EventRegistrationPricingOptionGroup;
};

export function CheckboxOptionGroup({ group, participantIndex }: CheckboxOptionGroupProps) {
  const { updatePricingOptions } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const selectedOptionIds = useMemo(() => {
    if (!registrationEntries) return [];
    return group.options
      .filter(option => registrationEntries[participantIndex].options?.includes(Number(option.optionId)))
      .map(option => Number(option.optionId));
  }, [registrationEntries, group, participantIndex]);

  const options = useMemo(
    () => group.options.filter(option => shouldDisplayField(option, participantIndex)),
    [group, participantIndex]
  );

  const updatePricingOption = useCallback(
    (optionId: number, checked: boolean) => {
      if (!registrationEntries) return;
      const newOptions = checked ? selectedOptionIds.concat(optionId) : selectedOptionIds.filter(id => id !== optionId);
      updatePricingOptions(participantIndex, Number(group.optionGroupId), newOptions);
    },
    [group, participantIndex, selectedOptionIds, updatePricingOptions, registrationEntries]
  );

  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const error = invalidFields.find(invF => invF.key === `${participantIndex}-${group.optionGroupId}`);
  const showError = forceErrorMessages && error;

  return (
    <div className="space-y-2">
      <h3 className={cn('text-md font-medium flex items-center', showError && 'border-destructive')}>
        {group.name} {group.required && <span className="text-destructive ml-1">*</span>}
      </h3>
      <div id={`${participantIndex}-${group.optionGroupId}`} className="space-y-2">
        {options.map(option => (
          <Label
            key={option.optionId}
            htmlFor={`${participantIndex}-${group.optionGroupId}-${option.optionId}`}
            className="flex items-center justify-between p-3 border rounded-md cursor-pointer"
          >
            <div className="flex items-center space-x-2">
              <Checkbox
                id={`${participantIndex}-${group.optionGroupId}-${option.optionId}`}
                checked={selectedOptionIds.includes(Number(option.optionId))}
                onCheckedChange={checked => {
                  updatePricingOption(Number(option.optionId), !!checked);
                }}
              />
              <div className="flex items-center">
                {option.name}
                {option.description && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground ml-2" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{option.description}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </div>
            {option.price && option.currency && (
              <div className="font-medium">
                {option.price.toFixed(2)} {option.currency}
              </div>
            )}
          </Label>
        ))}
      </div>
      {showError && <p className="text-sm text-destructive">{error?.message}</p>}
      {group.description && <p className="text-sm text-muted-foreground">{group.description}</p>}
    </div>
  );
}
