import { EventRegistrationPricingOptionGroup } from 'src/generated/api/dsv-public/model';
import { RadioOptionGroup } from 'src/options/radio-option-group';
import { CheckboxOptionGroup } from 'src/options/checkbox-option-group';

type OptionFieldProps = {
  participantIndex: number;
  group: EventRegistrationPricingOptionGroup;
};

export function OptionGroup({ group, participantIndex }: OptionFieldProps) {
  if (group.maxOptions === 0) {
    return null;
  }

  if (group.minOptions === 1 && group.maxOptions === 1 && group.required) {
    return <RadioOptionGroup participantIndex={participantIndex} group={group} />;
  }

  return <CheckboxOptionGroup participantIndex={participantIndex} group={group} />;
}
