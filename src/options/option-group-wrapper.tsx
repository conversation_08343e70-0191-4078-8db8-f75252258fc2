import { EventRegistrationPricingOptionGroup } from 'src/generated/api/dsv-public/model';
import { useMemo } from 'react';
import { shouldDisplayField } from 'src/utils/field-display-utils';
import { OptionGroup } from './option-group';

type BehaviorAwareOptionGroupProps = {
  group: EventRegistrationPricingOptionGroup;
  participantIndex: number;
};

export function OptionGroupWrapper({ group, participantIndex }: BehaviorAwareOptionGroupProps) {
  const shouldDisplay = useMemo(() => {
    return shouldDisplayField(group, participantIndex);
  }, [group, participantIndex]);

  if (!shouldDisplay) {
    return null;
  }

  return <OptionGroup group={group} participantIndex={participantIndex} />;
}
