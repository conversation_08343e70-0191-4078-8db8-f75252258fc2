import { FormStepper } from 'src/shared/form-stepper';
import { FormStep, useFormProgressStore } from 'src/stores/form-progress-store';
import { WelcomePage } from 'src/pages/welcome-page';
import { ParticipantsPage } from 'src/pages/participants-page';
import { RegistrantPage } from 'src/pages/registrant-page';
import { BillingPage } from 'src/pages/billing-page';
import { ConfirmationPage } from 'src/pages/confirmation-page';
import { ContactPage } from 'src/pages/contact-page';

export function RegistrationForm() {
  const currentStep = useFormProgressStore(state => state.currentStep);

  if (currentStep === FormStep.EVENT_DETAILS) {
    return (
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        <FormStepper />
        <WelcomePage />
      </div>
    );
  }

  if (currentStep === FormStep.PARTICIPANTS) {
    return (
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        <FormStepper />
        <ParticipantsPage />
      </div>
    );
  }

  if (currentStep === FormStep.EMERGENCY_CONTACT) {
    return (
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        <FormStepper />
        <ContactPage />
      </div>
    );
  }

  if (currentStep === FormStep.REGISTRANT) {
    return (
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        <FormStepper />
        <RegistrantPage />
      </div>
    );
  }

  if (currentStep === FormStep.BILLING) {
    return (
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        <FormStepper />
        <BillingPage />
      </div>
    );
  }

  if (currentStep === FormStep.CONFIRMATION) {
    return (
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        <FormStepper />
        <ConfirmationPage />
      </div>
    );
  }

  // ELSE SHOULD NEVER HAPPEN
  console.error('FormPage: currentStep is invalid', currentStep);
  return <div></div>;
}
