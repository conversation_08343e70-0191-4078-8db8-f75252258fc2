import { Button } from 'src/lib/components/ui/button';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { useFormContext } from 'src/context/form-context';
import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

type FormNavigationProps = {
  nextDisabled?: boolean;
  nextText?: string;
  onNext?: () => boolean | Promise<boolean>;
};

export function FormNavigation({ nextDisabled, nextText, onNext }: FormNavigationProps) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState<boolean>(false);
  const currentStep = useFormProgressStore(state => state.currentStep);
  const { availableSteps, navigateNextStep, navigatePreviousStep } = useFormContext();
  const currentStepIndex = availableSteps.findIndex(step => step === currentStep);
  const canGoBack = currentStepIndex > 0;
  const canGoNext = currentStepIndex < availableSteps.length - 1;

  const handleNext = async () => {
    if (!onNext) {
      navigateNextStep();
      return;
    }

    setLoading(true);
    const result = await onNext();
    if (result) {
      navigateNextStep();
    }
    setLoading(false);
  };

  if (canGoBack && canGoNext) {
    return (
      <div className="flex justify-between">
        <Button variant="outline" onClick={navigatePreviousStep}>
          {t('shared.navigation.back')}
        </Button>
        <Button onClick={handleNext} disabled={nextDisabled || loading}>
          {loading && <Loader2 className="animate-spin" />}
          {nextText || t('shared.navigation.next')}
        </Button>
      </div>
    );
  }

  if (!canGoBack && canGoNext) {
    return (
      <div className="flex justify-end">
        <Button onClick={handleNext} disabled={nextDisabled || loading}>
          {loading && <Loader2 className="animate-spin" />}
          {nextText || t('shared.navigation.next')}
        </Button>
      </div>
    );
  }

  if (canGoBack && !canGoNext) {
    return (
      <div className="flex">
        <Button variant="outline" onClick={navigatePreviousStep}>
          {t('shared.navigation.back')}
        </Button>
      </div>
    );
  }

  return <></>;
}
