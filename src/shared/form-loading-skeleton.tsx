import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'src/lib/components/ui/card';
import { Skeleton } from 'src/lib/components/ui/skeleton';

export function FormLoadingSkeleton() {
  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <div className="flex justify-center mb-8">
        <div className="flex items-center space-x-2">
          {[1, 2, 3, 4, 5].map(step => (
            <React.Fragment key={step}>
              <Skeleton className="h-8 w-8 rounded-full" />
              {step < 5 && <Skeleton className="h-1 w-16" />}
            </React.Fragment>
          ))}
        </div>
      </div>

      <Card className="w-full">
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-8 w-3/4" />
          </CardTitle>
          <Skeleton className="h-4 w-1/2 mt-2" />
        </Card<PERSON>eader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-4/6" />
          </div>

          <div className="space-y-3">
            <Skeleton className="h-10 w-full rounded-md" />
            <Skeleton className="h-10 w-full rounded-md" />
            <Skeleton className="h-10 w-full rounded-md" />
          </div>

          <div className="flex justify-end mt-6">
            <Skeleton className="h-10 w-32 rounded-md" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
