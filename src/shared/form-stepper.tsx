import { Check } from 'lucide-react';
import { FormStep, useFormProgressStore } from 'src/stores/form-progress-store';
import { useFormContext } from 'src/context/form-context';
import { cn } from 'src/lib/utils';
import { useTranslation } from 'react-i18next';

const getFormStepLabels = (t: (key: string) => string, isUnderageContactLabel: boolean) => ({
  [FormStep.EVENT_DETAILS]: t('stepper.event-details'),
  [FormStep.PARTICIPANTS]: t('stepper.participants'),
  [FormStep.EMERGENCY_CONTACT]: isUnderageContactLabel
    ? t('stepper.parental-responsibility')
    : t('stepper.emergency-contact'),
  [FormStep.REGISTRANT]: t('stepper.registrant'),
  [FormStep.BILLING]: t('stepper.billing'),
  [FormStep.CONFIRMATION]: t('stepper.confirmation'),
});

export function FormStepper() {
  const { t } = useTranslation();
  const currentStep = useFormProgressStore(state => state.currentStep);
  const { availableSteps, hasUnderageParticipant, form } = useFormContext();
  const currentStepIndex = availableSteps.findIndex(step => step === currentStep);
  const isUnderageContactLabel = hasUnderageParticipant && form.underageCheckEnabled;
  const FormStepLabels = getFormStepLabels(t, isUnderageContactLabel);

  return (
    <div className="w-full mb-8">
      <div className="hidden md:flex items-center">
        {availableSteps.map((step, index) => (
          <div key={index} className={cn('flex', index < availableSteps.length - 1 && 'grow')}>
            <div className="relative flex flex-col items-center">
              <div
                className={`w-10 h-10 flex items-center justify-center rounded-full border-2 z-10
                ${
                  index < currentStepIndex
                    ? 'bg-primary border-primary text-primary-foreground'
                    : index === currentStepIndex
                    ? 'border-primary text-primary'
                    : 'border-muted-foreground text-muted-foreground'
                }`}
              >
                {index < currentStepIndex ? <Check className="h-6 w-6" /> : <span>{index + 1}</span>}
              </div>
              <div className="text-center mt-2">
                <div
                  className={`text-sm font-medium ${
                    index <= currentStepIndex ? 'text-primary' : 'text-muted-foreground'
                  }`}
                >
                  {FormStepLabels[step]}
                </div>
              </div>
            </div>
            {index < availableSteps.length - 1 && (
              <div className="grow">
                <div
                  className={`relative top-5 w-full h-[2px]
                    ${index < currentStepIndex ? 'bg-primary' : 'bg-muted-foreground'}`}
                />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Mobile view */}
      <div className="md:hidden">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 flex items-center justify-center rounded-full border-2 border-primary text-primary">
            {currentStepIndex + 1}
          </div>
          <div>
            <div className="text-sm font-medium text-primary">{FormStepLabels[currentStep]}</div>
            <div className="text-xs text-muted-foreground">
              {t('stepper.step-count', { current: currentStepIndex + 1, total: availableSteps.length })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
