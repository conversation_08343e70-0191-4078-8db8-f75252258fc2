import { useFormContext } from 'src/context/form-context';
import { AlertTriangle, Info } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from 'src/lib/components/ui/alert';
import { prettyPrintDate } from 'src/lib/utils';
import { EventRegistrationFormStatus } from 'src/generated/api/dsv-public/model';
import { useTranslation } from 'react-i18next';

export function FormStatus() {
  const { form } = useFormContext();
  const { t } = useTranslation();

  if (form.status === EventRegistrationFormStatus.WAIT) {
    return (
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>{t('shared.form-status.wait.title')}</AlertTitle>
        <AlertDescription>
          {t('shared.form-status.wait.description', { date: prettyPrintDate(form.registrationStart) })}
        </AlertDescription>
      </Alert>
    );
  }

  if (form.status === EventRegistrationFormStatus.PRIVATE) {
    return (
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>{t('shared.form-status.private.title')}</AlertTitle>
        <AlertDescription>{t('shared.form-status.private.description')}</AlertDescription>
      </Alert>
    );
  }

  if (form.status === EventRegistrationFormStatus.WAITING_LIST) {
    return (
      <Alert variant="warn">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>{t('shared.form-status.waiting-list.title')}</AlertTitle>
        <AlertDescription>{t('shared.form-status.waiting-list.description')}</AlertDescription>
      </Alert>
    );
  }

  if (form.status === EventRegistrationFormStatus.FULL) {
    return (
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>{t('shared.form-status.full.title')}</AlertTitle>
        <AlertDescription>{t('shared.form-status.full.description')}</AlertDescription>
      </Alert>
    );
  }

  if (form.status === EventRegistrationFormStatus.CLOSED) {
    return (
      <Alert>
        <Info className="h-4 w-4" />
        <AlertTitle>{t('shared.form-status.closed.title')}</AlertTitle>
        <AlertDescription>{t('shared.form-status.closed.description')}</AlertDescription>
      </Alert>
    );
  }

  return <></>;
}
