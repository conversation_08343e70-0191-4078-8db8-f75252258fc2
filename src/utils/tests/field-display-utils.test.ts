import { describe, it, expect } from 'vitest';
import { shouldDisplayField } from '../field-display-utils';
import { EventRegistrationFormFieldBehavior } from 'src/generated/api/dsv-public/model';

describe('shouldDisplayField', () => {
  it('should return true for fields with ALL behavior', () => {
    const field = { behavior: EventRegistrationFormFieldBehavior.ALL };
    expect(shouldDisplayField(field as any, 0)).toBe(true);
    expect(shouldDisplayField(field as any, 1)).toBe(true);
    expect(shouldDisplayField(field as any, 2)).toBe(true);
    expect(shouldDisplayField(field as any, 3)).toBe(true);
  });

  it('should return true for fields with ALL_COPIED behavior', () => {
    const field = { behavior: EventRegistrationFormFieldBehavior.ALL_COPIED };
    expect(shouldDisplayField(field as any, 0)).toBe(true);
    expect(shouldDisplayField(field as any, 1)).toBe(true);
    expect(shouldDisplayField(field as any, 2)).toBe(true);
    expect(shouldDisplayField(field as any, 3)).toBe(true);
  });

  it('should only return true for first participant with FIRST behavior', () => {
    const field = { behavior: EventRegistrationFormFieldBehavior.FIRST };
    expect(shouldDisplayField(field as any, 0)).toBe(true);
    expect(shouldDisplayField(field as any, 1)).toBe(false);
    expect(shouldDisplayField(field as any, 2)).toBe(false);
    expect(shouldDisplayField(field as any, 3)).toBe(false);
  });

  it('should only return true for first participant with FIRST_APPLIED_TO_OTHERS behavior', () => {
    const field = { behavior: EventRegistrationFormFieldBehavior.FIRST_APPLIED_TO_OTHERS };
    expect(shouldDisplayField(field as any, 0)).toBe(true);
    expect(shouldDisplayField(field as any, 1)).toBe(false);
    expect(shouldDisplayField(field as any, 2)).toBe(false);
    expect(shouldDisplayField(field as any, 3)).toBe(false);
  });

  it('should only return true for second participant with SECOND behavior', () => {
    const field = { behavior: EventRegistrationFormFieldBehavior.SECOND };
    expect(shouldDisplayField(field as any, 0)).toBe(false);
    expect(shouldDisplayField(field as any, 1)).toBe(true);
    expect(shouldDisplayField(field as any, 2)).toBe(false);
    expect(shouldDisplayField(field as any, 3)).toBe(false);
  });

  it('should only return true for third participant with THIRD behavior', () => {
    const field = { behavior: EventRegistrationFormFieldBehavior.THIRD };
    expect(shouldDisplayField(field as any, 0)).toBe(false);
    expect(shouldDisplayField(field as any, 1)).toBe(false);
    expect(shouldDisplayField(field as any, 2)).toBe(true);
    expect(shouldDisplayField(field as any, 3)).toBe(false);
  });

  it('should only return true for participants with index >= 3 with MORE_THAN_THREE behavior', () => {
    const field = { behavior: EventRegistrationFormFieldBehavior.MORE_THAN_THREE };
    expect(shouldDisplayField(field as any, 0)).toBe(false);
    expect(shouldDisplayField(field as any, 1)).toBe(false);
    expect(shouldDisplayField(field as any, 2)).toBe(false);
    expect(shouldDisplayField(field as any, 3)).toBe(true);
    expect(shouldDisplayField(field as any, 4)).toBe(true);
  });

  it('should default to ALL behavior if behavior is not specified', () => {
    const field = {};
    expect(shouldDisplayField(field as any, 0)).toBe(true);
    expect(shouldDisplayField(field as any, 1)).toBe(true);
    expect(shouldDisplayField(field as any, 2)).toBe(true);
    expect(shouldDisplayField(field as any, 3)).toBe(true);
  });
});
