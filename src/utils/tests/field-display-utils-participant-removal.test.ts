import { describe, it, expect } from 'vitest';
import { shouldClearValueAfterParticipantRemoval } from '../field-display-utils';
import { EventRegistrationFormFieldBehavior } from 'src/generated/api/dsv-public/model';

describe('shouldClearOptionAfterParticipantRemoval', () => {
  it('should not clear options with ALL behavior', () => {
    const option = { behavior: EventRegistrationFormFieldBehavior.ALL };
    expect(shouldClearValueAfterParticipantRemoval(option, 0, 0)).toBe(false);
    expect(shouldClearValueAfterParticipantRemoval(option, 1, 0)).toBe(false);
    expect(shouldClearValueAfterParticipantRemoval(option, 2, 1)).toBe(false);
  });

  it('should not clear options with ALL_COPIED behavior', () => {
    const option = { behavior: EventRegistrationFormFieldBehavior.ALL_COPIED };
    expect(shouldClearValueAfterParticipantRemoval(option, 0, 0)).toBe(false);
    expect(shouldClearValueAfterParticipantRemoval(option, 1, 0)).toBe(false);
    expect(shouldClearValueAfterParticipantRemoval(option, 2, 1)).toBe(false);
  });

  it('should clear SECOND behavior option when participant 1 becomes participant 0', () => {
    const option = { behavior: EventRegistrationFormFieldBehavior.SECOND };
    // Participant 1 has SECOND option selected, participant 0 is removed
    // Participant 1 becomes participant 0, but SECOND option is only visible to participant 1
    expect(shouldClearValueAfterParticipantRemoval(option, 1, 0)).toBe(true);
  });

  it('should not clear SECOND behavior option when participant after is removed', () => {
    const option = { behavior: EventRegistrationFormFieldBehavior.SECOND };
    // Participant 1 has SECOND option selected, participant 2 is removed
    // Participant 1 stays participant 1, so SECOND option should remain
    expect(shouldClearValueAfterParticipantRemoval(option, 1, 2)).toBe(false);
  });

  it('should clear THIRD behavior option when participant 2 becomes participant 1', () => {
    const option = { behavior: EventRegistrationFormFieldBehavior.THIRD };
    // Participant 2 has THIRD option selected, participant 0 is removed
    // Participant 2 becomes participant 1, but THIRD option is only visible to participant 2
    expect(shouldClearValueAfterParticipantRemoval(option, 2, 0)).toBe(true);
  });

  it('should not clear THIRD behavior option when it remains at correct position', () => {
    const option = { behavior: EventRegistrationFormFieldBehavior.THIRD };
    // Participant 2 has THIRD option selected, participant 3 is removed
    // Participant 2 stays participant 2, so THIRD option should remain
    expect(shouldClearValueAfterParticipantRemoval(option, 2, 3)).toBe(false);
  });

  it('should clear FIRST behavior option when participant 0 is removed', () => {
    const option = { behavior: EventRegistrationFormFieldBehavior.FIRST };
    // This is an edge case - if participant 0 had FIRST option and is removed,
    // no other participant should have this option
    expect(shouldClearValueAfterParticipantRemoval(option, 0, 0)).toBe(false); // The participant being removed
  });

  it('should not clear FIRST behavior option for participant 0 when another is removed', () => {
    const option = { behavior: EventRegistrationFormFieldBehavior.FIRST };
    // Participant 0 has FIRST option, participant 1 is removed
    // Participant 0 stays participant 0, so FIRST option should remain
    expect(shouldClearValueAfterParticipantRemoval(option, 0, 1)).toBe(false);
  });

  it('should clear MORE_THAN_THREE behavior option when participant index drops below 3', () => {
    const option = { behavior: EventRegistrationFormFieldBehavior.MORE_THAN_THREE };
    // Participant 3 has MORE_THAN_THREE option, participant 0 is removed
    // Participant 3 becomes participant 2, but MORE_THAN_THREE is only visible to participants >= 3
    expect(shouldClearValueAfterParticipantRemoval(option, 3, 0)).toBe(true);
  });

  it('should not clear MORE_THAN_THREE behavior option when participant stays >= 3', () => {
    const option = { behavior: EventRegistrationFormFieldBehavior.MORE_THAN_THREE };
    // Participant 4 has MORE_THAN_THREE option, participant 0 is removed
    // Participant 4 becomes participant 3, which is still >= 3
    expect(shouldClearValueAfterParticipantRemoval(option, 4, 0)).toBe(false);
  });

  it('should handle undefined behavior as ALL behavior', () => {
    const option = {}; // No behavior specified
    expect(shouldClearValueAfterParticipantRemoval(option, 1, 0)).toBe(false);
  });

  it('should handle complex scenario with multiple participants', () => {
    const secondOption = { behavior: EventRegistrationFormFieldBehavior.SECOND };
    const thirdOption = { behavior: EventRegistrationFormFieldBehavior.THIRD };

    // Remove participant 0, so indices shift down by 1
    // Participant 1 (with SECOND option) becomes participant 0 -> should clear SECOND
    expect(shouldClearValueAfterParticipantRemoval(secondOption, 1, 0)).toBe(true);

    // Participant 2 (with THIRD option) becomes participant 1 -> should clear THIRD
    expect(shouldClearValueAfterParticipantRemoval(thirdOption, 2, 0)).toBe(true);
  });
});
