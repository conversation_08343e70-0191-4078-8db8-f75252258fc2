import dayjs, { Dayjs } from 'dayjs';

export const getDecimalAge = (birthDate: Dayjs, eventDate: Dayjs) => {
  const years = eventDate.diff(birthDate, 'year');
  const remainder = eventDate.diff(birthDate.add(years, 'year'), 'day');
  const daysInYear = birthDate.add(years, 'year').isLeapYear() ? 366 : 365;
  return years + remainder / daysInYear;
};

export const isUnderage = (birthdate: string) => {
  return getDecimalAge(dayjs(birthdate), dayjs()) < 18;
};
