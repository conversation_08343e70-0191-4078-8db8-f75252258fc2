import React, { useEffect, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18nMod from 'src/i18n/i18n';
import 'dayjs/locale/de';
import { useFormStore, ErrorType } from 'src/stores/form-store';
import { RegistrationForm } from 'src/registration-form';
import { FormProvider } from 'src/context/form-context';
import { FormLoadingSkeleton } from 'src/shared/form-loading-skeleton';
import { ErrorPage } from 'src/pages/components/error-page';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { EventRegistrationType } from 'src/generated/api/dsv-public/model';

type RegistrationFormWrapperProps = {
  eventId?: number;
};

export const RegistrationFormWrapper: React.FC<RegistrationFormWrapperProps> = props => {
  const [searchParams] = useSearchParams();

  const eventId: number = useMemo(() => {
    if (props.eventId) {
      return props.eventId;
    }
    const eventIdString = searchParams.get('eventId');
    if (eventIdString != null) {
      return parseInt(eventIdString);
    }
    return -1;
  }, [searchParams, props.eventId]);

  const isLoading = useFormStore(state => state.isLoading);
  const event = useFormStore(state => state.event);
  const form = useFormStore(state => state.form);
  const error = useFormStore(state => state.error);
  const registrationSelection = useFormProgressStore(state => state.registrationSelection);
  const setRegistrationSelection = useFormProgressStore(state => state.setRegistrationSelection);

  useEffect(() => {
    if (eventId <= 0 && !isLoading && !error) {
      useFormStore.getState().setError(ErrorType.NO_EVENT_ID);
    } else if (!event && eventId > 0 && !isLoading && !error) {
      useFormStore.getState().init(eventId);
    }
  }, [eventId, isLoading, event, error]);

  useEffect(() => {
    if (
      form?.registeredBySelections?.length === 1 ||
      (form?.registeredBySelections?.includes(EventRegistrationType.PARTICIPANT) && !registrationSelection)
    ) {
      const defaultSelection = form?.registeredBySelections?.includes(EventRegistrationType.PARTICIPANT)
        ? EventRegistrationType.PARTICIPANT
        : form.registeredBySelections[0];
      setRegistrationSelection(defaultSelection);
    }
  }, [form, registrationSelection]);

  return (
    <div className="App">
      <I18nextProvider i18n={i18nMod}>
        {isLoading && <FormLoadingSkeleton />}
        {!isLoading && error && <ErrorPage errorType={error} />}
        {!isLoading && !error && event && form && (
          <FormProvider eventId={eventId} event={event} form={form}>
            <RegistrationForm />
          </FormProvider>
        )}
      </I18nextProvider>
    </div>
  );
};
