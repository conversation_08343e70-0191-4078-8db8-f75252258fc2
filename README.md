# Registration From React Component

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

### Prerequisites

You need to have the following software before setting up the environment.

- Git ([download instructions](https://git-scm.com/downloads))
- Node.js ^22.12.0 (can be installed with [nvm](https://github.com/nvm-sh/nvm#installing-and-updating))
  ```sh
  nvm use
  ```
- npm ^10.9.0 (as a part of Node.js)
- `yarn`

Then you need to run `yarn install` in order to download all dependencies and `yarn msw init public/ --save` to generate the mock service worker.

## Development

In the project directory, you can run:

### `yarn mock`

Runs the app in the development mode. With a mocked backend\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `yarn local`

Runs the app in the development mode. Uses the backend running on localhost.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `yarn dev`

Runs the app in the development mode. Uses the backend running on the dev server.\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `yarn start`

Runs the app in the development mode. Uses the productive backend. (Should be changed to a dev backend?)\
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.\
You will also see any lint errors in the console.

### `yarn test`

Launches the test runner in the interactive watch mode.

### `yarn test:ci`

Launches the test runner once, without the interactive watch mode.

### `yarn build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.\
The build is minified and the filenames include the hashes.

### `yarn eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Prettier configuration

Fix your code formatting:

* Check: `node_modules/.bin/prettier --check`
* Fix: `node_modules/.bin/prettier --write`

## Usage

To access a registration form you need to pss the `eventId` as query parameter: `http://localhost:3000/?eventId=<eventId>`

## Deployment

Every push gets automatically deployed to `https://adventisten.gitlab.io/termindatenbank/registration-form/`.

So this url will show the latest pushed change, regardless of the branch name.

## Embedding as web component

Include the hosted javascript file in the body:
```html
<body>
	<script src="<insert host url here>/static/js/bundle.js"></script>
	<!-- content... -->
</body>
```

And then insert the custom component `registration-form` where you like in the document.
You can pass the `event-id` as parameter. Alternatively, you can provide the event id as url parameter `eventId` like in the usage section.

```html
<body>
    <!-- content... -->
    <registration-form event-id="84953"></registration-form>
    <!-- content... -->
</body>
```


