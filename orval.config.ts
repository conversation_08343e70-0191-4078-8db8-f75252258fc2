import { defineConfig } from 'orval';

export default defineConfig({
  'dsv-public': {
    input: {
      target: './dsv-public-api.yaml',
    },
    output: {
      target: 'src/generated/api/dsv-public/dsv-public.ts',
      schemas: 'src/generated/api/dsv-public/model',
      headers: true,
      mode: 'split',
      mock: true,
      clean: true,
      override: {
        mutator: {
          path: './src/dsv-api-axios-instance.ts',
          name: 'dsvApiAxiosInstance',
        },
      },
    },
  },
});
