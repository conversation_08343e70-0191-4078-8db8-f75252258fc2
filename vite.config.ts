import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import viteTsconfigPaths from 'vite-tsconfig-paths';
import path from 'path';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  base: '/',
  plugins: [react(), viteTsconfigPaths(), tailwindcss()],
  build: {
    rollupOptions: {
      output: {
        // Remove hashes from filenames
        entryFileNames: 'static/js/[name].js',
        chunkFileNames: 'static/js/[name].chunk.js',
        assetFileNames: assetInfo => {
          if (assetInfo.name?.endsWith('.css')) {
            return 'static/css/[name][extname]';
          }
          return 'static/media/[name][extname]';
        },
      },
    },
  },
  resolve: {
    alias: {
      src: path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
  },
});
