stages:
  - build
  - test
  - deploy

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - when: always

cache:
  key: $CI_PROJECT_ID
  policy: pull
  untracked: true

build-job:
  image: node:latest
  stage: build
  cache:
    key: $CI_PROJECT_ID
    policy: pull-push
    paths:
      - .yarn-cache/
  variables:
    PUBLIC_URL: .
  script:
    - yarn install --cache-folder .yarn-cache
    - yarn build
  artifacts:
    paths:
      - dist/
    expire_in: 2 weeks

test-job:
  image: node:latest
  stage: test
  script:
    - yarn install --cache-folder .yarn-cache
    - yarn lint
    - yarn prettier src --check
    - yarn test:ci

pages:
  image: alpine:latest
  stage: deploy
  variables:
    GIT_STRATEGY: none
  script:
    - mv dist public
  artifacts:
    paths:
      - public

#### Deploy ####

.ssh-deploy-template-gitlab:
  before_script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - mkdir -p ~/.ssh
    - eval $(ssh-agent -s)
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
    - ssh-add <(echo "$SSH_DEPLOY_PRIVATE_KEY")

.deploy-code-changes:
  extends: .ssh-deploy-template-gitlab
  stage: deploy
  image: registry.gitlab.com/adventisten/ci-cd-shared/util:1
  needs:
  - build-job
  script:
  - mkdir -p deploy/$DEPLOY_VERSION
  - cp -r dist/* deploy/$DEPLOY_VERSION/
  - cd deploy
  - 'echo "<?php define(\"VERSION\", \"$DEPLOY_VERSION\"); define(\"BASE_URL\", \"$BASE_URL\");" > config.php'
  - rsync -rav -e 'ssh -p2121' ./ $SSH_LOGIN:$SSH_PATH

deploy-dev:
  extends: .deploy-code-changes
  variables:
    SSH_DEPLOY_PRIVATE_KEY: $GITLAB_DEPLOY_PRIVATE_KEY
    SSH_LOGIN: <EMAIL>
    SSH_PATH: '/dev.termine.adventisten.ch/registration-form/'
    DEPLOY_VERSION: $CI_COMMIT_SHORT_SHA
    BASE_URL: https://dev.termine.adventisten.cloud
  rules:
  - if: '$CI_COMMIT_REF_NAME =~ /main/'

deploy-prod:
  extends: .deploy-code-changes
  variables:
    SSH_DEPLOY_PRIVATE_KEY: $GITLAB_DEPLOY_PRIVATE_KEY
    SSH_LOGIN: <EMAIL>
    SSH_PATH: '/httpdocs/registration-form/'
    DEPLOY_VERSION: $CI_COMMIT_TAG
    BASE_URL: https://termine.adventisten.cloud
  rules:
  - if: '$CI_COMMIT_TAG'
